{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  {{ form.name }}
{% endblock %}

{% block extra_css %}
  <link rel="stylesheet" href="{% static 'css/output.css' %}" />
{% endblock %}

{% block content %}
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <a href="{% url 'forms:form_list' %}" class="text-gray-500 hover:text-gray-700">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
            </a>
            <div>
              <h1 class="text-3xl font-bold text-gray-900">{{ form.name }}</h1>
              <p class="mt-2 text-gray-600">{{ form.description|default:'No description' }}</p>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                       {% if form.status == 'published' %}
                
                
                
                bg-green-100 text-green-800




              {% elif form.status == 'draft' %}
                
                
                
                bg-yellow-100 text-yellow-800




              {% elif form.status == 'archived' %}
                
                
                
                bg-gray-100 text-gray-800




              {% else %}
                
                
                
                bg-blue-100 text-blue-800



              {% endif %}">
              {{ form.get_status_display }}
            </span>

            <a href="{% url 'forms:form_edit' form.slug %}" class="btn btn-outline">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>Edit
            </a>

            <a href="{% url 'forms:form_builder' form.slug %}" class="btn btn-primary">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
              </svg>Form Builder
            </a>
          </div>
        </div>
      </div>

      <!-- Form Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Fields</p>
                <p class="text-2xl font-semibold text-gray-900">{{ form.fields.count }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Submissions</p>
                <p class="text-2xl font-semibold text-gray-900">{{ form.submission_count }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Views</p>
                <p class="text-2xl font-semibold text-gray-900">-</p>
              </div>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-body">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-9 0v10a2 2 0 002 2h8a2 2 0 002-2V7H7z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Created</p>
                <p class="text-2xl font-semibold text-gray-900">{{ form.created_at|date:'M d' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Preview -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900">Form Preview</h3>
        </div>
        <div class="card-body">
          <div id="form-preview-app" data-form-slug="{{ form.slug }}" data-api-base-url="{% url 'forms:form-list' %}api">
            <!-- Vue.js Form Preview will mount here -->
            <div class="text-center py-8">
              <div class="loading-spinner mx-auto mb-4"></div>
              <p class="text-gray-500">Loading form preview...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  {% load_vite_assets 'form-renderer' %}
{% endblock %}
