<!-- Form Builder Canvas Component -->
<div class="flex-1 p-6">
  <div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
      <!-- Form Header -->
      <div class="mb-6">
        <h2 class="text-xl font-bold text-gray-900">{{ formData.name }}</h2>
        <p v-if="formData.description" class="text-gray-600 mt-1">{{ formData.description }}</p>
      </div>

      <!-- Form Fields -->
      <div v-if="formFields.length > 0" class="space-y-6">
        <div v-for="(field, index) in formFields" :key="field.id" 
             @click="selectField(field)"
             :class="{'ring-2 ring-blue-500 bg-blue-50': selectedField && selectedField.id === field.id}"
             class="relative p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-gray-300 hover:shadow-sm transition-all duration-200 group">
          
          <!-- Field Order Badge -->
          <div class="absolute -top-2 -left-2 w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
            {{ index + 1 }}
          </div>

          <!-- Field Label -->
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ field.label }}
            <span v-if="field.required" class="text-red-500 ml-1">*</span>
          </label>

          <!-- Field Input (Preview) -->
          <div v-if="field.type === 'text' || field.type === 'email' || field.type === 'number'">
            <input :type="field.type" 
                   :placeholder="field.placeholder" 
                   class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                   disabled>
          </div>
          
          <div v-else-if="field.type === 'textarea'">
            <textarea :placeholder="field.placeholder" 
                      rows="3"
                      class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                      disabled></textarea>
          </div>
          
          <div v-else-if="field.type === 'select'">
            <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" disabled>
              <option value="">Choose an option</option>
              <option v-for="option in field.options" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
          
          <div v-else-if="field.type === 'radio'">
            <div class="space-y-2">
              <label v-for="option in field.options" :key="option.value" class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded">
                <input type="radio" 
                       :name="field.name" 
                       :value="option.value" 
                       class="mr-3 text-blue-600 focus:ring-blue-500" 
                       disabled>
                <span class="text-sm text-gray-700">{{ option.label }}</span>
              </label>
            </div>
          </div>
          
          <div v-else-if="field.type === 'checkbox'">
            <div class="space-y-2">
              <label v-for="option in field.options" :key="option.value" class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded">
                <input type="checkbox" 
                       :value="option.value" 
                       class="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                       disabled>
                <span class="text-sm text-gray-700">{{ option.label }}</span>
              </label>
            </div>
          </div>
          
          <div v-else-if="field.type === 'date'">
            <input type="date" 
                   class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                   disabled>
          </div>
          
          <div v-else-if="field.type === 'file'">
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              <p class="text-sm text-gray-500">Click to upload or drag and drop</p>
            </div>
          </div>

          <!-- Help Text -->
          <p v-if="field.help_text" class="text-xs text-gray-500 mt-2 flex items-center">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ field.help_text }}
          </p>

          <!-- Field Actions -->
          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
            <button @click.stop="duplicateField(field)" 
                    class="p-1 text-blue-500 hover:text-blue-700 bg-white rounded shadow-sm border border-gray-200"
                    title="Duplicate field">
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </button>
            <button @click.stop="removeField(field.id)" 
                    class="p-1 text-red-500 hover:text-red-700 bg-white rounded shadow-sm border border-gray-200"
                    title="Remove field">
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>

          <!-- Field Type Badge -->
          <div class="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              {{ field.type }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Form</h3>
        <p class="text-gray-500 mb-4">Click on field types in the sidebar to add them to your form</p>
        <div class="flex justify-center space-x-2 text-sm text-gray-400">
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
            </svg>
            Click to add
          </span>
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Click to edit
          </span>
        </div>
      </div>

      <!-- Form Actions (if fields exist) -->
      <div v-if="formFields.length > 0" class="mt-8 pt-6 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <p class="text-sm text-gray-500">{{ formFields.length }} field{{ formFields.length !== 1 ? 's' : '' }} added</p>
          <button type="submit" class="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50" disabled>
            Submit Form
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
