<!-- Form Builder Sidebar Component -->
<div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
  <div class="p-6">
    <!-- Sidebar Tabs -->
    <div class="flex border-b border-gray-200 mb-4">
      <button @click="activeTab = 'fields'" 
              :class="{'border-blue-500 text-blue-600': activeTab === 'fields', 'border-transparent text-gray-500': activeTab !== 'fields'}" 
              class="px-4 py-2 border-b-2 font-medium text-sm hover:text-blue-600 transition-colors">
        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Fields
      </button>
      <button @click="activeTab = 'design'" 
              :class="{'border-blue-500 text-blue-600': activeTab === 'design', 'border-transparent text-gray-500': activeTab !== 'design'}" 
              class="px-4 py-2 border-b-2 font-medium text-sm hover:text-blue-600 transition-colors">
        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
        </svg>
        Design
      </button>
      <button @click="activeTab = 'settings'" 
              :class="{'border-blue-500 text-blue-600': activeTab === 'settings', 'border-transparent text-gray-500': activeTab !== 'settings'}" 
              class="px-4 py-2 border-b-2 font-medium text-sm hover:text-blue-600 transition-colors">
        <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Settings
      </button>
    </div>

    <!-- Fields Tab -->
    <div v-if="activeTab === 'fields'" class="animate-fade-in">
      <h3 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
        </svg>
        Field Types
      </h3>
      <div class="space-y-2">
        <div v-for="fieldType in availableFieldTypes" :key="fieldType.id" 
             @click="addField(fieldType)"
             class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm transition-all duration-200 group">
          <div class="flex items-center">
            <div :class="'w-8 h-8 bg-' + fieldType.color + '-100 rounded flex items-center justify-center mr-3 group-hover:bg-' + fieldType.color + '-200 transition-colors'">
              <span :class="'text-' + fieldType.color + '-600 text-sm font-medium'">{% verbatim %}{{ fieldType.icon }}{% endverbatim %}</span>
            </div>
            <div class="flex-1">
              <p class="font-medium text-gray-900 group-hover:text-gray-700">{% verbatim %}{{ fieldType.name }}{% endverbatim %}</p>
              <p class="text-xs text-gray-500">{% verbatim %}{{ fieldType.description }}{% endverbatim %}</p>
            </div>
            <svg class="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Design Tab -->
    <div v-if="activeTab === 'design'" class="animate-fade-in">
      <h3 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
        </svg>
        Form Styling
      </h3>
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
          <select v-model="formData.customization.theme" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
            <option value="default">Default</option>
            <option value="modern">Modern</option>
            <option value="minimal">Minimal</option>
            <option value="classic">Classic</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
          <div class="flex items-center space-x-2">
            <input v-model="formData.customization.colors.primary" type="color" class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer">
            <input v-model="formData.customization.colors.primary" type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm font-mono">
          </div>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Form Width</label>
          <select v-model="formData.customization.layout.width" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
            <option value="narrow">Narrow (600px)</option>
            <option value="medium">Medium (800px)</option>
            <option value="wide">Wide (1000px)</option>
            <option value="full">Full Width</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Field Spacing</label>
          <select v-model="formData.customization.layout.spacing" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
            <option value="compact">Compact</option>
            <option value="normal">Normal</option>
            <option value="relaxed">Relaxed</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Settings Tab -->
    <div v-if="activeTab === 'settings'" class="animate-fade-in">
      <h3 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Form Settings
      </h3>
      <div class="space-y-4">
        <div class="space-y-3">
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input v-model="formData.settings.allowMultipleSubmissions" type="checkbox" class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
            <div>
              <span class="text-sm font-medium text-gray-700">Allow multiple submissions</span>
              <p class="text-xs text-gray-500">Users can submit this form multiple times</p>
            </div>
          </label>
          
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input v-model="formData.settings.requireLogin" type="checkbox" class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
            <div>
              <span class="text-sm font-medium text-gray-700">Require login to submit</span>
              <p class="text-xs text-gray-500">Only authenticated users can submit</p>
            </div>
          </label>
          
          <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
            <input v-model="formData.settings.collectEmail" type="checkbox" class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
            <div>
              <span class="text-sm font-medium text-gray-700">Collect email addresses</span>
              <p class="text-xs text-gray-500">Automatically collect submitter's email</p>
            </div>
          </label>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Success Message</label>
          <textarea v-model="formData.settings.successMessage" 
                    class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                    rows="3" 
                    placeholder="Thank you for your submission!"></textarea>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Redirect URL (Optional)</label>
          <input v-model="formData.settings.redirectUrl" 
                 type="url" 
                 class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                 placeholder="https://example.com/thank-you">
        </div>
      </div>
    </div>
  </div>
</div>
