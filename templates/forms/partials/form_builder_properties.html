<!-- Field Properties Panel Component -->
<div v-if="selectedField" class="w-80 bg-white border-l border-gray-200 p-6 overflow-y-auto">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      </svg>
      Field Properties
    </h3>
    <button @click="selectedField = null" class="p-1 text-gray-400 hover:text-gray-600">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <!-- Field Type Display -->
  <div class="mb-6 p-3 bg-gray-50 rounded-lg">
    <div class="flex items-center">
      <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
        <span class="text-blue-600 text-sm font-medium">{{ getFieldTypeIcon(selectedField.type) }}</span>
      </div>
      <div>
        <p class="font-medium text-gray-900">{{ getFieldTypeName(selectedField.type) }}</p>
        <p class="text-xs text-gray-500">{{ selectedField.type }} field</p>
      </div>
    </div>
  </div>
  
  <div class="space-y-6">
    <!-- Basic Properties -->
    <div class="space-y-4">
      <h4 class="text-sm font-medium text-gray-900 border-b border-gray-200 pb-2">Basic Properties</h4>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Field Label
          <span class="text-red-500">*</span>
        </label>
        <input v-model="selectedField.label" 
               type="text" 
               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
               placeholder="Enter field label">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Field Name
          <span class="text-xs text-gray-500">(used in form data)</span>
        </label>
        <input v-model="selectedField.name" 
               type="text" 
               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm font-mono focus:ring-blue-500 focus:border-blue-500"
               placeholder="field_name">
      </div>
      
      <div v-if="selectedField.type !== 'checkbox' && selectedField.type !== 'radio'">
        <label class="block text-sm font-medium text-gray-700 mb-2">Placeholder Text</label>
        <input v-model="selectedField.placeholder" 
               type="text" 
               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
               placeholder="Enter placeholder text">
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Help Text</label>
        <textarea v-model="selectedField.help_text" 
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                  rows="2"
                  placeholder="Additional help or instructions"></textarea>
      </div>
    </div>

    <!-- Field Behavior -->
    <div class="space-y-4">
      <h4 class="text-sm font-medium text-gray-900 border-b border-gray-200 pb-2">Field Behavior</h4>
      
      <div class="space-y-3">
        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
          <input v-model="selectedField.required" 
                 type="checkbox" 
                 class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
          <div>
            <span class="text-sm font-medium text-gray-700">Required field</span>
            <p class="text-xs text-gray-500">Users must fill this field</p>
          </div>
        </label>
        
        <label v-if="selectedField.type === 'text' || selectedField.type === 'textarea'" 
               class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
          <input v-model="selectedField.properties.readonly" 
                 type="checkbox" 
                 class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
          <div>
            <span class="text-sm font-medium text-gray-700">Read-only</span>
            <p class="text-xs text-gray-500">Display only, cannot be edited</p>
          </div>
        </label>
        
        <label v-if="selectedField.type !== 'file'" 
               class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
          <input v-model="selectedField.properties.hidden" 
                 type="checkbox" 
                 class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
          <div>
            <span class="text-sm font-medium text-gray-700">Hidden field</span>
            <p class="text-xs text-gray-500">Not visible to users</p>
          </div>
        </label>
      </div>
    </div>

    <!-- Validation Rules -->
    <div v-if="selectedField.type === 'text' || selectedField.type === 'textarea' || selectedField.type === 'number'" class="space-y-4">
      <h4 class="text-sm font-medium text-gray-900 border-b border-gray-200 pb-2">Validation Rules</h4>
      
      <div v-if="selectedField.type === 'text' || selectedField.type === 'textarea'" class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Min Length</label>
          <input v-model.number="selectedField.validation_rules.minLength" 
                 type="number" 
                 min="0"
                 class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Max Length</label>
          <input v-model.number="selectedField.validation_rules.maxLength" 
                 type="number" 
                 min="0"
                 class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
        </div>
      </div>
      
      <div v-if="selectedField.type === 'number'" class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Min Value</label>
          <input v-model.number="selectedField.validation_rules.min" 
                 type="number"
                 class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Max Value</label>
          <input v-model.number="selectedField.validation_rules.max" 
                 type="number"
                 class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
        </div>
      </div>
      
      <div v-if="selectedField.type === 'text'">
        <label class="block text-sm font-medium text-gray-700 mb-2">Pattern (Regex)</label>
        <input v-model="selectedField.validation_rules.pattern" 
               type="text" 
               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm font-mono focus:ring-blue-500 focus:border-blue-500"
               placeholder="^[A-Za-z]+$">
        <p class="text-xs text-gray-500 mt-1">Regular expression for validation</p>
      </div>
    </div>

    <!-- Options for select, radio, checkbox fields -->
    <div v-if="selectedField.type === 'select' || selectedField.type === 'radio' || selectedField.type === 'checkbox'" class="space-y-4">
      <h4 class="text-sm font-medium text-gray-900 border-b border-gray-200 pb-2 flex items-center justify-between">
        Options
        <span class="text-xs text-gray-500 font-normal">{{ selectedField.options.length }} option{{ selectedField.options.length !== 1 ? 's' : '' }}</span>
      </h4>
      
      <div class="space-y-3">
        <div v-for="(option, index) in selectedField.options" :key="index" 
             class="flex items-center space-x-2 p-3 border border-gray-200 rounded-lg">
          <div class="flex-1 space-y-2">
            <input v-model="option.label" 
                   type="text" 
                   class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500" 
                   placeholder="Option label">
            <input v-model="option.value" 
                   type="text" 
                   class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm font-mono focus:ring-blue-500 focus:border-blue-500" 
                   placeholder="option_value">
          </div>
          <div class="flex flex-col space-y-1">
            <button @click="moveOptionUp(index)" 
                    :disabled="index === 0"
                    class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-30"
                    title="Move up">
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
              </svg>
            </button>
            <button @click="moveOptionDown(index)" 
                    :disabled="index === selectedField.options.length - 1"
                    class="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-30"
                    title="Move down">
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <button @click="selectedField.options.splice(index, 1)" 
                    class="p-1 text-red-500 hover:text-red-700"
                    title="Remove option">
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <button @click="addOption" 
                class="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-sm text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Option
        </button>
      </div>
    </div>

    <!-- File Upload Settings -->
    <div v-if="selectedField.type === 'file'" class="space-y-4">
      <h4 class="text-sm font-medium text-gray-900 border-b border-gray-200 pb-2">File Upload Settings</h4>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Accepted File Types</label>
        <input v-model="selectedField.properties.accept" 
               type="text" 
               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
               placeholder=".pdf,.doc,.docx,image/*">
        <p class="text-xs text-gray-500 mt-1">Comma-separated file extensions or MIME types</p>
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Max File Size (MB)</label>
        <input v-model.number="selectedField.properties.maxSize" 
               type="number" 
               min="1"
               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500">
      </div>
      
      <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
        <input v-model="selectedField.properties.multiple" 
               type="checkbox" 
               class="rounded border-gray-300 text-blue-600 mr-3 focus:ring-blue-500">
        <div>
          <span class="text-sm font-medium text-gray-700">Allow multiple files</span>
          <p class="text-xs text-gray-500">Users can upload multiple files</p>
        </div>
      </label>
    </div>

    <!-- Field Actions -->
    <div class="pt-6 border-t border-gray-200">
      <div class="flex space-x-2">
        <button @click="duplicateField(selectedField)" 
                class="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
          Duplicate
        </button>
        <button @click="removeField(selectedField.id); selectedField = null" 
                class="flex-1 px-3 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Delete
        </button>
      </div>
    </div>
  </div>
</div>
