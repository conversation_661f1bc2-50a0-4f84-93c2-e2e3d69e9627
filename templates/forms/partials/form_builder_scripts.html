{% load static %}
{% load vite_tags %}
<!-- Form Builder JavaScript Initialization -->

<!-- Form Builder Data -->
<script>
  console.log('Setting up comprehensive form builder data...')
  
  try {
    // Parse JSON data safely
    const fieldsData = JSON.parse('{{ form_fields_json|escapejs }}')
    const settingsData = JSON.parse('{{ form_settings_json|escapejs }}')
    const customizationData = JSON.parse('{{ form_customization_json|escapejs }}')
  
    // Comprehensive form data setup with all features
    window.formBuilderData = {
      formData: {
        id: '{{ form.id }}',
        name: '{{ form.name|escapejs }}',
        description: '{{ form.description|escapejs }}',
        status: '{{ form.status|escapejs }}',
        fields: fieldsData,
        settings: settingsData,
        customization: customizationData
      },
      csrfToken: '{{ csrf_token }}',
      apiBaseUrl: '/api/v1',
      formSlug: '{{ form.slug }}'
    }
  
    console.log('Comprehensive Form Builder Data:', window.formBuilderData)
  } catch (error) {
    console.error('Error parsing form builder data:', error)
    // Fallback data
    window.formBuilderData = {
      formData: {
        id: '{{ form.id }}',
        name: '{{ form.name|escapejs }}',
        description: '{{ form.description|escapejs }}',
        status: '{{ form.status|escapejs }}',
        fields: [],
        settings: {
          allowMultipleSubmissions: false,
          requireLogin: false,
          collectEmail: false,
          successMessage: 'Thank you for your submission!',
          redirectUrl: ''
        },
        customization: {
          theme: 'default',
          colors: {
            primary: '#3B82F6'
          },
          layout: {
            width: 'medium',
            spacing: 'normal'
          }
        }
      },
      csrfToken: '{{ csrf_token }}',
      apiBaseUrl: '/api/v1',
      formSlug: '{{ form.slug }}'
    }
  }
</script>

<!-- Load Vue.js and Form Builder Assets -->
{% load_vite_assets 'form-builder' %}

<!-- Comprehensive Form Builder Initialization -->
<script>
  // Initialize the form builder application
  document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded - Initializing Comprehensive Form Builder')
  
    const formBuilderElement = document.getElementById('form-builder-app')
    console.log('Form Builder Element:', formBuilderElement)
    console.log('Form Builder Data:', window.formBuilderData)
  
    if (!formBuilderElement) {
      console.error('Form builder element not found!')
      return
    }
  
    if (!window.formBuilderData) {
      console.error('Form builder data not found!')
      return
    }
  
    try {
      console.log('Initializing comprehensive Vue app with all features...')
  
      // Check if the initFormBuilder function is available (from Vite build)
      if (typeof window.initFormBuilder === 'function') {
        console.log('Using Vite-built form builder')
        const app = window.initFormBuilder('#form-builder-app', window.formBuilderData.formData, window.formBuilderData.csrfToken)
        console.log('Comprehensive Vue app initialized successfully:', app)
      } else {
        console.log('Vite build not available, using fallback implementation')
        initFallbackFormBuilder()
      }
    } catch (error) {
      console.error('Error initializing comprehensive form builder:', error)
      initFallbackFormBuilder()
    }
  })
  
  // Fallback form builder implementation
  function initFallbackFormBuilder() {
    console.log('Initializing fallback form builder...')
  
    // Hide loading state
    const loadingState = document.getElementById('loading-state')
    if (loadingState) {
      loadingState.style.display = 'none'
    }
  
    // Check if Vue is available from CDN
    if (typeof Vue !== 'undefined') {
      const { createApp } = Vue
  
      const app = createApp({
        data() {
          return {
            formData: window.formBuilderData?.formData || {},
            csrfToken: window.formBuilderData?.csrfToken || '',
            activeTab: 'fields',
            selectedField: null,
            draggedField: null,
            formFields: window.formBuilderData?.formData?.fields || [],
            availableFieldTypes: [
              { id: 'text', name: 'Text Input', icon: 'T', color: 'blue', description: 'Single line text field' },
              { id: 'email', name: 'Email', icon: '@', color: 'green', description: 'Email address field' },
              { id: 'textarea', name: 'Textarea', icon: '¶', color: 'indigo', description: 'Multi-line text field' },
              { id: 'select', name: 'Select', icon: '▼', color: 'purple', description: 'Dropdown selection' },
              { id: 'radio', name: 'Radio', icon: '◉', color: 'pink', description: 'Single choice' },
              { id: 'checkbox', name: 'Checkbox', icon: '☐', color: 'orange', description: 'Multiple choice' },
              { id: 'number', name: 'Number', icon: '#', color: 'red', description: 'Numeric input' },
              { id: 'date', name: 'Date', icon: '📅', color: 'yellow', description: 'Date picker' },
              { id: 'file', name: 'File Upload', icon: '📎', color: 'gray', description: 'File attachment' }
            ]
          }
        },
        mounted() {
          console.log('Fallback Vue app mounted successfully')
          console.log('Form fields loaded:', this.formFields.length)
        },
        methods: {
          // Field Management
          addField(fieldType) {
            const newField = {
              id: 'field_' + Date.now(),
              type: fieldType.id,
              name: fieldType.name.toLowerCase().replace(/\s+/g, '_') + '_' + Date.now(),
              label: fieldType.name,
              required: false,
              placeholder: '',
              help_text: '',
              order: this.formFields.length,
              properties: this.getDefaultProperties(fieldType.id),
              validation_rules: this.getDefaultValidationRules(fieldType.id),
              options: this.getDefaultOptions(fieldType.id)
            }
            this.formFields.push(newField)
            this.selectedField = newField
            console.log('Added field:', newField)
          },
  
          removeField(fieldId) {
            this.formFields = this.formFields.filter((field) => field.id !== fieldId)
            if (this.selectedField && this.selectedField.id === fieldId) {
              this.selectedField = null
            }
            console.log('Removed field:', fieldId)
          },
  
          duplicateField(field) {
            const duplicatedField = {
              ...JSON.parse(JSON.stringify(field)),
              id: 'field_' + Date.now(),
              name: field.name + '_copy',
              label: field.label + ' (Copy)'
            }
            this.formFields.push(duplicatedField)
            this.selectedField = duplicatedField
            console.log('Duplicated field:', duplicatedField)
          },
  
          selectField(field) {
            this.selectedField = field
            console.log('Selected field:', field)
          },
  
          // Field Type Helpers
          getFieldTypeIcon(type) {
            const fieldType = this.availableFieldTypes.find((ft) => ft.id === type)
            return fieldType ? fieldType.icon : '?'
          },
  
          getFieldTypeName(type) {
            const fieldType = this.availableFieldTypes.find((ft) => ft.id === type)
            return fieldType ? fieldType.name : type
          },
  
          // Default Properties
          getDefaultProperties(fieldType) {
            const defaults = {
              text: { readonly: false, hidden: false },
              email: { readonly: false, hidden: false },
              textarea: { readonly: false, hidden: false, rows: 3 },
              number: { readonly: false, hidden: false, step: 1 },
              date: { readonly: false, hidden: false },
              file: { accept: '', maxSize: 10, multiple: false },
              select: { multiple: false },
              radio: {},
              checkbox: {}
            }
            return defaults[fieldType] || {}
          },
  
          getDefaultValidationRules(fieldType) {
            const defaults = {
              text: { minLength: null, maxLength: null, pattern: '' },
              textarea: { minLength: null, maxLength: null },
              number: { min: null, max: null },
              email: {},
              date: {},
              file: {},
              select: {},
              radio: {},
              checkbox: {}
            }
            return defaults[fieldType] || {}
          },
  
          getDefaultOptions(fieldType) {
            if (['select', 'radio', 'checkbox'].includes(fieldType)) {
              return [
                { value: 'option1', label: 'Option 1' },
                { value: 'option2', label: 'Option 2' }
              ]
            }
            return []
          },
  
          // Option Management
          addOption() {
            if (this.selectedField && this.selectedField.options) {
              const optionNumber = this.selectedField.options.length + 1
              this.selectedField.options.push({
                value: 'option' + optionNumber,
                label: 'Option ' + optionNumber
              })
            }
          },
  
          moveOptionUp(index) {
            if (index > 0 && this.selectedField && this.selectedField.options) {
              const options = this.selectedField.options
              const temp = options[index]
              options[index] = options[index - 1]
              options[index - 1] = temp
            }
          },
  
          moveOptionDown(index) {
            if (this.selectedField && this.selectedField.options && index < this.selectedField.options.length - 1) {
              const options = this.selectedField.options
              const temp = options[index]
              options[index] = options[index + 1]
              options[index + 1] = temp
            }
          },
  
          // Form Actions
          saveForm() {
            console.log('Saving form with fields:', this.formFields)
            console.log('Form data:', this.formData)
  
            // TODO: Implement actual save functionality
            const formData = {
              ...this.formData,
              fields: this.formFields
            }
  
            // Show success message for now
            alert('Form saved successfully! (This will be replaced with actual save functionality)')
          },
  
          previewForm() {
            console.log('Previewing form')
            // TODO: Implement preview functionality
            const previewUrl = `/forms/${this.formData.id}/preview/`
            window.open(previewUrl, '_blank')
          }
        },
        template: `
            <div class="min-h-screen bg-gray-50">
              {% include 'forms/partials/form_builder_header.html' %}
              
              <div class="flex h-screen">
                {% include 'forms/partials/form_builder_sidebar.html' %}
                {% include 'forms/partials/form_builder_canvas.html' %}
                {% include 'forms/partials/form_builder_properties.html' %}
              </div>
            </div>
          `
      })
  
      console.log('Mounting Vue app...')
      app.mount('#form-builder-app')
      console.log('Vue app mounted successfully')
    } else {
      console.error('Vue.js not available')
      showFallback()
    }
  }
  
  function showFallback() {
    console.log('Showing fallback content')
    const loadingState = document.getElementById('loading-state')
    const fallbackContent = document.getElementById('fallback-content')
  
    if (loadingState) {
      loadingState.style.display = 'none'
    }
    if (fallbackContent) {
      fallbackContent.classList.remove('hidden')
    }
  }
</script>

<!-- Add some custom CSS for animations -->
<style>
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }
</style>
