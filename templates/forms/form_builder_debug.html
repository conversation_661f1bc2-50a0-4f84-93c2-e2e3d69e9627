{% extends 'base.html' %}
{% load static %}

{% block title %}
  Form Builder Debug - PDFlex
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-4">Form Builder Debug</h1>
    
    <div class="bg-gray-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">Form Data</h2>
      <p><strong>ID:</strong> {{ form.id }}</p>
      <p><strong>Name:</strong> {{ form.name }}</p>
      <p><strong>Status:</strong> {{ form.status }}</p>
      <p><strong>Fields Count:</strong> {{ form.fields.count }}</p>
    </div>
    
    <div class="bg-gray-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">JSON Data</h2>
      <h3 class="font-medium">Fields JSON:</h3>
      <pre class="bg-white p-2 rounded text-sm overflow-auto">{{ form_fields_json }}</pre>
      
      <h3 class="font-medium mt-4">Settings JSON:</h3>
      <pre class="bg-white p-2 rounded text-sm overflow-auto">{{ form_settings_json }}</pre>
      
      <h3 class="font-medium mt-4">Customization JSON:</h3>
      <pre class="bg-white p-2 rounded text-sm overflow-auto">{{ form_customization_json }}</pre>
    </div>
    
    <div class="bg-gray-100 p-4 rounded">
      <h2 class="text-lg font-semibold mb-2">JavaScript Test</h2>
      <div id="js-test">Loading...</div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    console.log('Debug page loaded');
    
    // Test basic JavaScript
    document.getElementById('js-test').innerHTML = 'JavaScript is working!';
    
    // Test JSON parsing
    try {
      const fieldsJson = {{ form_fields_json|safe }};
      console.log('Fields JSON parsed successfully:', fieldsJson);
    } catch (error) {
      console.error('Error parsing fields JSON:', error);
    }
    
    try {
      const settingsJson = {{ form_settings_json|safe }};
      console.log('Settings JSON parsed successfully:', settingsJson);
    } catch (error) {
      console.error('Error parsing settings JSON:', error);
    }
    
    try {
      const customizationJson = {{ form_customization_json|safe }};
      console.log('Customization JSON parsed successfully:', customizationJson);
    } catch (error) {
      console.error('Error parsing customization JSON:', error);
    }
  </script>
{% endblock %}
