{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="min-h-screen bg-gray-50">
    <!-- Loading state while Vue app initializes -->
    <div id="loading-state" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-600 text-lg font-medium">Loading Form Builder...</p>
        <p class="text-sm text-gray-500 mt-2">Initializing Vue.js interface...</p>
      </div>
    </div>

    <!-- Vue.js will replace this content -->
    <div id="vue-app-content" style="display: none;">
      <!-- Form Builder Header -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">{{ form.name }}</h1>
            <span class="ml-3 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">{{ form.status }}</span>
          </div>
          <div class="flex items-center space-x-3">
            <button id="preview-btn" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Preview</button>
            <button id="save-btn" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">Save Form</button>
          </div>
        </div>
      </div>

      <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
          <div class="p-6">
            <h3 class="text-sm font-medium text-gray-900 mb-3">Available Fields</h3>
            <div id="field-types" class="space-y-2">
              <!-- Field types will be populated by Vue.js -->
            </div>
          </div>
        </div>

        <!-- Canvas -->
        <div id="canvas" class="flex-1 bg-gray-50 overflow-y-auto">
          <div class="p-8">
            <div class="max-w-2xl mx-auto">
              <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="mb-6">
                  <h2 class="text-lg font-semibold text-gray-900">{{ form.name }}</h2>
                  {% if form.description %}
                    <p class="text-sm text-gray-600 mt-1">{{ form.description }}</p>
                  {% endif %}
                </div>

                <div id="form-fields" class="space-y-4">
                  <!-- Form fields will be populated by Vue.js -->
                  <div id="empty-state" class="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <p class="text-gray-500 text-lg font-medium">No fields added yet</p>
                    <p class="text-gray-400 text-sm mt-1">Click on fields from the sidebar to add them</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Properties Panel -->
        <div id="properties-panel" class="w-80 bg-white border-l border-gray-200 p-6 overflow-y-auto" style="display: none;">
          <!-- Properties will be populated by Vue.js -->
        </div>
      </div>
    </div>

    <!-- Fallback content for non-JS users -->
    <div id="fallback-content" class="hidden min-h-screen bg-gray-50 p-8">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow">
          <h1 class="text-2xl font-bold text-gray-900 mb-4">Form Builder</h1>
          <p class="text-gray-600 mb-4">JavaScript is required to use the form builder interface.</p>
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-blue-800 font-medium">Form Information:</p>
            <p class="text-blue-700 mt-1">Name: {{ form.name }}</p>
            <p class="text-blue-700">Status: {{ form.status }}</p>
            <p class="text-blue-700">ID: {{ form.id }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Include Form Builder JavaScript Components -->
  {% include 'forms/partials/form_builder_scripts.html' %}
{% endblock %}
