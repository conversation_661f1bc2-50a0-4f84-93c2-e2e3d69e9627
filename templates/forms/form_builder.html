{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="min-h-screen">
    <!-- Loading state while Vue app initializes -->
    <div id="loading-state" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-600 text-lg font-medium">Loading Form Builder...</p>
        <p class="text-sm text-gray-500 mt-2">Initializing modular Vue.js interface...</p>
        <div class="mt-4 text-xs text-gray-400">
          <p>✓ Header component ready</p>
          <p>✓ Sidebar component ready</p>
          <p>✓ Canvas component ready</p>
          <p>✓ Properties panel ready</p>
        </div>
      </div>
    </div>

    <!-- Fallback content for non-JS users -->
    <div id="fallback-content" class="hidden min-h-screen bg-gray-50 p-8">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow">
          <h1 class="text-2xl font-bold text-gray-900 mb-4">Form Builder</h1>
          <p class="text-gray-600 mb-4">JavaScript is required to use the form builder interface.</p>
          <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-blue-800 font-medium">Form Information:</p>
            <p class="text-blue-700 mt-1">Name: {{ form.name }}</p>
            <p class="text-blue-700">Status: {{ form.status }}</p>
            <p class="text-blue-700">ID: {{ form.id }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Include Form Builder JavaScript Components -->
  {% include 'forms/partials/form_builder_scripts.html' %}
{% endblock %}
