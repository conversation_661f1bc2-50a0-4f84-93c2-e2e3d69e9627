{% extends 'base.html' %}
{% load static %}

{% block title %}
  Create Form
{% endblock %}

{% block extra_css %}
  <link rel="stylesheet" href="{% static 'css/output.css' %}" />
{% endblock %}

{% block content %}
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center space-x-4">
          <a href="{% url 'forms:form_list' %}" class="text-gray-500 hover:text-gray-700">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
          </a>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Create New Form</h1>
            <p class="mt-2 text-gray-600">Start building your form</p>
          </div>
        </div>
      </div>

      <!-- Messages -->
      {% if messages %}
        <div class="mb-6">
          {% for message in messages %}
            <div class="alert alert-{{ message.tags }} mb-4">
              <div class="flex items-center">
                {% if message.tags == 'success' %}
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                {% elif message.tags == 'error' %}
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                {% endif %}
                {{ message }}
              </div>
            </div>
          {% endfor %}
        </div>
      {% endif %}

      <!-- Create Form Options -->
      <div class="space-y-6">
        <!-- Start from Scratch -->
        <div class="card">
          <div class="card-body">
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Start from Scratch</h3>
                <p class="mt-1 text-gray-500">Create a completely custom form with our drag-and-drop builder.</p>
                <div class="mt-4">
                  <form method="post" action="{% url 'forms:form_create' %}" id="create-form" novalidate>
                    {% csrf_token %}
                    <div class="grid grid-cols-1 gap-4">
                      <div>
                        <label for="name" class="field-label">Form Name <span class="field-required">*</span></label>
                        <input type="text" id="name" name="name" required class="form-input" placeholder="Enter form name" minlength="3" maxlength="200" />
                        <div class="field-error" id="name-error"></div>
                      </div>
                      <div>
                        <label for="description" class="field-label">Description</label>
                        <textarea id="description" name="description" rows="3" class="form-input" placeholder="Describe your form (optional)" maxlength="1000"></textarea>
                        <div class="field-error" id="description-error"></div>
                      </div>

                      <!-- Advanced Options (Collapsible) -->
                      <div class="border-t border-gray-200 pt-4">
                        <button type="button" id="toggle-advanced" class="flex items-center text-sm text-gray-600 hover:text-gray-900">
                          <svg class="w-4 h-4 mr-2 transform transition-transform" id="advanced-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                          </svg>Advanced Options
                        </button>

                        <div id="advanced-options" class="hidden mt-4 space-y-4">
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label for="submission_handling" class="field-label">Submission Handling</label>
                              <select id="submission_handling" name="submission_handling" class="form-input">
                                <option value="database">Store in Database</option>
                                <option value="email">Email Notifications</option>
                                <option value="webhook">Webhook Integration</option>
                                <option value="pdf">PDF Generation</option>
                              </select>
                            </div>

                            <div>
                              <label for="max_submissions" class="field-label">Max Submissions</label>
                              <input type="number" id="max_submissions" name="max_submissions" class="form-input" placeholder="Unlimited" min="1" max="10000" />
                            </div>
                          </div>

                          <div class="space-y-3">
                            <div class="flex items-center">
                              <input type="checkbox" id="is_public" name="is_public" class="form-checkbox" />
                              <label for="is_public" class="ml-2 text-sm text-gray-700">Make form publicly accessible</label>
                            </div>

                            <div class="flex items-center">
                              <input type="checkbox" id="requires_authentication" name="requires_authentication" class="form-checkbox" />
                              <label for="requires_authentication" class="ml-2 text-sm text-gray-700">Require user authentication</label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="mt-4">
                      <button type="submit" class="btn btn-primary" id="create-btn">
                        <span class="btn-text">Create Form</span>
                        <span class="btn-loading hidden">
                          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>Creating...
                        </span>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Templates Section -->
        <div class="card">
          <div class="card-header">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-medium text-gray-900">Use a Template</h3>
                <p class="text-sm text-gray-500">Get started quickly with pre-built form templates</p>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div id="form-templates-app" data-api-base-url="{% url 'forms:form-list' %}api">
              <!-- Vue.js Templates List will mount here -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Loading placeholders -->
                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div class="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
                    <div class="h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>

                <div class="border border-gray-200 rounded-lg p-4">
                  <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                    <div class="h-3 bg-gray-200 rounded w-3/4 mb-3"></div>
                    <div class="h-8 bg-gray-200 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script type="module" src="{% static 'js/form-templates/main.js' %}"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const form = document.getElementById('create-form')
      const nameInput = document.getElementById('name')
      const descriptionInput = document.getElementById('description')
      const createBtn = document.getElementById('create-btn')
      const btnText = createBtn.querySelector('.btn-text')
      const btnLoading = createBtn.querySelector('.btn-loading')
      const toggleAdvanced = document.getElementById('toggle-advanced')
      const advancedOptions = document.getElementById('advanced-options')
      const advancedIcon = document.getElementById('advanced-icon')
    
      // Form validation
      function validateField(field, errorElementId, validationFn) {
        const errorElement = document.getElementById(errorElementId)
        const isValid = validationFn(field.value)
    
        if (isValid === true) {
          field.classList.remove('border-red-300')
          field.classList.add('border-gray-300')
          errorElement.textContent = ''
          errorElement.classList.add('hidden')
        } else {
          field.classList.remove('border-gray-300')
          field.classList.add('border-red-300')
          errorElement.textContent = isValid
          errorElement.classList.remove('hidden')
        }
    
        return isValid === true
      }
    
      function validateName(value) {
        if (!value.trim()) return 'Form name is required'
        if (value.trim().length < 3) return 'Form name must be at least 3 characters'
        if (value.length > 200) return 'Form name must be less than 200 characters'
        return true
      }
    
      function validateDescription(value) {
        if (value.length > 1000) return 'Description must be less than 1000 characters'
        return true
      }
    
      // Real-time validation
      nameInput.addEventListener('blur', function () {
        validateField(this, 'name-error', validateName)
      })
    
      descriptionInput.addEventListener('blur', function () {
        validateField(this, 'description-error', validateDescription)
      })
    
      // Advanced options toggle
      toggleAdvanced.addEventListener('click', function () {
        const isHidden = advancedOptions.classList.contains('hidden')
    
        if (isHidden) {
          advancedOptions.classList.remove('hidden')
          advancedIcon.style.transform = 'rotate(90deg)'
        } else {
          advancedOptions.classList.add('hidden')
          advancedIcon.style.transform = 'rotate(0deg)'
        }
      })
    
      // Form submission
      form.addEventListener('submit', function (e) {
        const nameValid = validateField(nameInput, 'name-error', validateName)
        const descriptionValid = validateField(descriptionInput, 'description-error', validateDescription)
    
        if (!nameValid || !descriptionValid) {
          e.preventDefault()
          return
        }
    
        // Show loading state
        createBtn.disabled = true
        btnText.classList.add('hidden')
        btnLoading.classList.remove('hidden')
      })
    })
  </script>
{% endblock %}
