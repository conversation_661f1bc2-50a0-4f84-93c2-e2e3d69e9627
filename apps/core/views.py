from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse


def home(request):
    """Home page view"""
    return render(request, 'core/landing.html')


def landing(request):
    """Landing page view"""
    return render(request, 'core/landing.html')


@login_required
def dashboard(request):
    """Dashboard view - redirects to user dashboard"""
    return redirect('users:dashboard')


def debug_user(request):
    """Debug view to check user authentication status"""
    if request.user.is_authenticated:
        return HttpResponse(f"Logged in as: {request.user.username} ({request.user.email})")
    else:
        return HttpResponse("Not logged in")
