"""
Template tags for Vite asset integration.
"""

import json
import os
from django import template
from django.conf import settings
from django.templatetags.static import static

register = template.Library()


def get_vite_manifest():
    """Load and cache the Vite manifest file."""
    # Don't cache in development to allow for hot reloading
    if settings.DEBUG or not hasattr(get_vite_manifest, '_manifest'):
        manifest_path = os.path.join(settings.BASE_DIR, 'static', 'dist', '.vite', 'manifest.json')
        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
                if not settings.DEBUG:
                    get_vite_manifest._manifest = manifest
                return manifest
        except (FileNotFoundError, json.JSONDecodeError):
            manifest = {}
            if not settings.DEBUG:
                get_vite_manifest._manifest = manifest
            return manifest
    return get_vite_manifest._manifest


@register.simple_tag
def vite_asset(entry_name):
    """
    Get the built asset path for a Vite entry point.
    
    Usage: {% vite_asset 'form-builder' %}
    Returns: /static/dist/js/form-builder-CntfZLdm.js
    """
    manifest = get_vite_manifest()
    
    # Try to find the entry in the manifest
    entry_key = f"static/js/{entry_name}/main.js"
    if entry_key in manifest:
        return static(f"dist/{manifest[entry_key]['file']}")
    
    # Fallback to direct path if manifest not found
    return static(f"js/{entry_name}/main.js")


@register.simple_tag
def vite_css(entry_name):
    """
    Get the CSS file path for a Vite entry point.
    
    Usage: {% vite_css 'form-builder' %}
    Returns: /static/dist/css/form-builder-BRbs27MZ.css
    """
    manifest = get_vite_manifest()
    
    # Try to find the entry in the manifest
    entry_key = f"static/js/{entry_name}/main.js"
    if entry_key in manifest and 'css' in manifest[entry_key]:
        css_files = manifest[entry_key]['css']
        if css_files:
            return static(f"dist/{css_files[0]}")
    
    # Fallback to direct path if manifest not found
    return static(f"css/{entry_name}.css")


@register.simple_tag
def vite_assets(entry_name):
    """
    Get both JS and CSS assets for a Vite entry point.
    
    Usage: {% vite_assets 'form-builder' as assets %}
    Returns: {'js': '/static/dist/js/...', 'css': '/static/dist/css/...'}
    """
    return {
        'js': vite_asset(entry_name),
        'css': vite_css(entry_name)
    }


@register.inclusion_tag('core/vite_assets.html')
def load_vite_assets(entry_name):
    """
    Load both CSS and JS assets for a Vite entry point.
    
    Usage: {% load_vite_assets 'form-builder' %}
    """
    manifest = get_vite_manifest()
    entry_key = f"static/js/{entry_name}/main.js"
    
    js_url = vite_asset(entry_name)
    css_url = None
    
    if entry_key in manifest and 'css' in manifest[entry_key]:
        css_files = manifest[entry_key]['css']
        if css_files:
            css_url = static(f"dist/{css_files[0]}")
    
    return {
        'js_url': js_url,
        'css_url': css_url,
        'entry_name': entry_name
    }
