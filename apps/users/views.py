from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import CreateView, UpdateView, DetailView, TemplateView
from django.views.generic.edit import FormView
from django.contrib.auth.views import LoginView, LogoutView, PasswordResetView, PasswordResetConfirmView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse, HttpResponseRedirect
from django.core.mail import send_mail
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.contrib.auth.tokens import default_token_generator
from django.template.loader import render_to_string
from django.contrib.sites.shortcuts import get_current_site
from django.utils import timezone
import uuid

from .models import User, UserProfile
from .forms import (
    CustomUserCreationForm,
    CustomAuthenticationForm,
    CustomPasswordResetForm,
    UserProfileForm,
    UserAccountForm
)


class CustomLoginView(LoginView):
    """Custom login view with enhanced functionality."""
    form_class = CustomAuthenticationForm
    template_name = 'users/login.html'
    redirect_authenticated_user = True

    def form_valid(self, form):
        """Handle successful login."""
        remember_me = form.cleaned_data.get('remember_me')
        if remember_me:
            self.request.session.set_expiry(1209600)  # 2 weeks
        else:
            self.request.session.set_expiry(0)  # Browser close

        # Update last activity
        user = form.get_user()
        user.last_activity = timezone.now()
        user.save(update_fields=['last_activity'])

        messages.success(self.request, _('Welcome back, {}!').format(user.get_display_name()))
        return super().form_valid(form)

    def get_success_url(self):
        """Redirect to dashboard after login."""
        return self.get_redirect_url() or reverse_lazy('core:dashboard')


class CustomLogoutView(LogoutView):
    """Custom logout view."""
    template_name = 'users/logout.html'

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.info(request, _('You have been logged out successfully.'))
        return super().dispatch(request, *args, **kwargs)


class RegisterView(CreateView):
    """User registration view."""
    model = User
    form_class = CustomUserCreationForm
    template_name = 'users/register.html'
    success_url = reverse_lazy('users:registration_complete')

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('core:dashboard')
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        """Handle successful registration."""
        response = super().form_valid(form)
        user = self.object

        # Send verification email
        self.send_verification_email(user)

        messages.success(
            self.request,
            _('Registration successful! Please check your email to verify your account.')
        )
        return response

    def send_verification_email(self, user):
        """Send email verification link."""
        current_site = get_current_site(self.request)
        subject = _('Verify your PDFLEX account')

        context = {
            'user': user,
            'domain': current_site.domain,
            'uid': urlsafe_base64_encode(force_bytes(user.pk)),
            'token': default_token_generator.make_token(user),
            'protocol': 'https' if self.request.is_secure() else 'http',
        }

        message = render_to_string('users/emails/verification_email.html', context)

        send_mail(
            subject,
            message,
            getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            [user.email],
            html_message=message,
            fail_silently=True,
        )


class RegistrationCompleteView(TemplateView):
    """Registration completion page."""
    template_name = 'users/registration_complete.html'


class EmailVerificationView(TemplateView):
    """Email verification view."""
    template_name = 'users/email_verification.html'

    def get(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            user = None

        if user is not None and default_token_generator.check_token(user, token):
            user.email_verified = True
            user.save(update_fields=['email_verified'])
            messages.success(request, _('Your email has been verified successfully!'))
            return redirect('users:login')
        else:
            messages.error(request, _('The verification link is invalid or has expired.'))
            return redirect('users:register')


class CustomPasswordResetView(PasswordResetView):
    """Custom password reset view."""
    form_class = CustomPasswordResetForm
    template_name = 'users/password_reset.html'
    email_template_name = 'users/emails/password_reset_email.html'
    success_url = reverse_lazy('users:password_reset_done')

    def form_valid(self, form):
        messages.success(
            self.request,
            _('Password reset instructions have been sent to your email.')
        )
        return super().form_valid(form)


class ProfileView(LoginRequiredMixin, DetailView):
    """User profile view."""
    model = User
    template_name = 'users/profile.html'
    context_object_name = 'profile_user'

    def get_object(self):
        """Get the user profile to display."""
        username = self.kwargs.get('username')
        if username:
            return get_object_or_404(User, username=username)
        return self.request.user

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['is_own_profile'] = self.object == self.request.user
        return context


class ProfileEditView(LoginRequiredMixin, UpdateView):
    """Edit user profile view."""
    model = UserProfile
    form_class = UserProfileForm
    template_name = 'users/profile_edit.html'

    def get_object(self):
        """Get the user's profile."""
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile

    def get_success_url(self):
        return reverse('users:profile')

    def form_valid(self, form):
        messages.success(self.request, _('Your profile has been updated successfully.'))
        return super().form_valid(form)


class AccountEditView(LoginRequiredMixin, UpdateView):
    """Edit user account information."""
    model = User
    form_class = UserAccountForm
    template_name = 'users/account_edit.html'

    def get_object(self):
        return self.request.user

    def get_success_url(self):
        return reverse('users:profile')

    def form_valid(self, form):
        messages.success(self.request, _('Your account information has been updated successfully.'))
        return super().form_valid(form)


class DashboardView(LoginRequiredMixin, TemplateView):
    """User dashboard view."""
    template_name = 'users/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        # Add dashboard statistics
        context.update({
            'user': user,
            'recent_activity': self.get_recent_activity(),
            'stats': self.get_user_stats(),
            'recent_forms': self.get_recent_forms(),
            'recent_templates': self.get_recent_templates(),
        })
        return context

    def get_recent_activity(self):
        """Get recent user activity."""
        from apps.forms.models import Form, FormSubmission
        from apps.pdf_generation.models import PDFTemplate, GeneratedCode

        user = self.request.user
        activities = []

        # Recent form creations
        recent_forms = Form.objects.filter(created_by=user).order_by('-created_at')[:3]
        for form in recent_forms:
            activities.append({
                'icon': 'file-text',
                'description': f'Created form "{form.name}"',
                'timestamp': form.created_at,
                'type': 'form_created'
            })

        # Recent template creations
        recent_templates = PDFTemplate.objects.filter(created_by=user).order_by('-created_at')[:3]
        for template in recent_templates:
            activities.append({
                'icon': 'file-pdf',
                'description': f'Created PDF template "{template.name}"',
                'timestamp': template.created_at,
                'type': 'template_created'
            })

        # Recent form submissions
        recent_submissions = FormSubmission.objects.filter(
            form__created_by=user
        ).order_by('-created_at')[:3]
        for submission in recent_submissions:
            activities.append({
                'icon': 'paper-plane',
                'description': f'New submission for "{submission.form.name}"',
                'timestamp': submission.created_at,
                'type': 'submission_received'
            })

        # Recent code generations
        recent_generations = GeneratedCode.objects.filter(
            generated_by=user
        ).order_by('-created_at')[:3]
        for generation in recent_generations:
            activities.append({
                'icon': 'code',
                'description': f'Generated {generation.language} code',
                'timestamp': generation.created_at,
                'type': 'code_generated'
            })

        # Sort by timestamp and return latest 10
        activities.sort(key=lambda x: x['timestamp'], reverse=True)
        return activities[:10]

    def get_user_stats(self):
        """Get user statistics."""
        from apps.forms.models import Form, FormSubmission
        from apps.pdf_generation.models import PDFTemplate, GeneratedCode

        user = self.request.user

        # Count user's forms
        total_forms = Form.objects.filter(created_by=user).count()

        # Count user's PDF templates
        total_templates = PDFTemplate.objects.filter(created_by=user).count()

        # Count submissions to user's forms
        total_submissions = FormSubmission.objects.filter(form__created_by=user).count()

        # Count code generations/downloads
        total_downloads = GeneratedCode.objects.filter(generated_by=user).count()

        return {
            'member_since': user.date_joined,
            'last_login': user.last_login,
            'email_verified': user.email_verified,
            'profile_complete': self.is_profile_complete(),
            'total_forms': total_forms,
            'total_templates': total_templates,
            'total_submissions': total_submissions,
            'total_downloads': total_downloads,
        }

    def is_profile_complete(self):
        """Check if user profile is complete."""
        user = self.request.user
        profile = getattr(user, 'profile', None)

        required_fields = [
            user.first_name,
            user.last_name,
            user.email,
        ]

        if profile:
            required_fields.extend([
                profile.bio,
                profile.phone,
            ])

        return all(field for field in required_fields)

    def get_recent_forms(self):
        """Get user's recent forms."""
        from apps.forms.models import Form

        user = self.request.user
        return Form.objects.filter(created_by=user).order_by('-updated_at')[:5]

    def get_recent_templates(self):
        """Get user's recent PDF templates."""
        from apps.pdf_generation.models import PDFTemplate

        user = self.request.user
        return PDFTemplate.objects.filter(created_by=user).order_by('-updated_at')[:5]


# Legacy function-based views for backward compatibility
@login_required
def profile(request, username=None):
    """Legacy profile view - redirects to class-based view."""
    if username:
        return redirect('users:profile_detail', username=username)
    return redirect('users:profile')


@login_required
def settings(request):
    """Legacy settings view - redirects to profile edit."""
    return redirect('users:profile_edit')
