"""
Management command to create sample data for testing the dashboard.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import uuid

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample data for testing the dashboard'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='testuser',
            help='Username for the test user',
        )
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Email for the test user',
        )
        parser.add_argument(
            '--password',
            type=str,
            default='testpass123',
            help='Password for the test user',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create or get test user
        user = self.create_test_user(options)
        
        # Create sample forms
        self.create_sample_forms(user)
        
        # Create sample PDF templates
        self.create_sample_templates(user)
        
        # Create sample submissions
        self.create_sample_submissions(user)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample data for user: {user.username}'
            )
        )

    def create_test_user(self, options):
        """Create or get the test user."""
        username = options['username']
        email = options['email']
        password = options['password']
        
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'first_name': 'Test',
                'last_name': 'User',
                'email_verified': True,
            }
        )
        
        if created:
            user.set_password(password)
            user.save()
            self.stdout.write(f'Created test user: {username}')
        else:
            self.stdout.write(f'Using existing user: {username}')
            
        return user

    def create_sample_forms(self, user):
        """Create sample forms."""
        try:
            from apps.forms.models import Form
            
            sample_forms = [
                {
                    'name': 'Contact Form',
                    'description': 'A simple contact form for website visitors',
                    'status': 'published'
                },
                {
                    'name': 'Event Registration',
                    'description': 'Registration form for upcoming events',
                    'status': 'published'
                },
                {
                    'name': 'Feedback Survey',
                    'description': 'Customer feedback and satisfaction survey',
                    'status': 'draft'
                },
            ]
            
            for form_data in sample_forms:
                form, created = Form.objects.get_or_create(
                    name=form_data['name'],
                    created_by=user,
                    defaults={
                        'description': form_data['description'],
                        'slug': form_data['name'].lower().replace(' ', '-'),
                        'status': form_data['status'],
                        'created_at': timezone.now() - timedelta(days=5),
                        'updated_at': timezone.now() - timedelta(days=2),
                    }
                )
                
                if created:
                    self.stdout.write(f'Created form: {form.name}')
                    
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Forms app not available, skipping form creation')
            )

    def create_sample_templates(self, user):
        """Create sample PDF templates."""
        try:
            from apps.pdf_generation.models import PDFTemplate
            
            sample_templates = [
                {
                    'name': 'Invoice Template',
                    'description': 'Professional invoice template with company branding',
                },
                {
                    'name': 'Certificate Template',
                    'description': 'Certificate of completion template',
                },
                {
                    'name': 'Report Template',
                    'description': 'Monthly report template with charts and tables',
                },
            ]
            
            for template_data in sample_templates:
                template, created = PDFTemplate.objects.get_or_create(
                    name=template_data['name'],
                    created_by=user,
                    defaults={
                        'description': template_data['description'],
                        'slug': template_data['name'].lower().replace(' ', '-'),
                        'status': 'active',
                        'created_at': timezone.now() - timedelta(days=7),
                        'updated_at': timezone.now() - timedelta(days=1),
                    }
                )
                
                if created:
                    self.stdout.write(f'Created template: {template.name}')
                    
        except ImportError:
            self.stdout.write(
                self.style.WARNING('PDF generation app not available, skipping template creation')
            )

    def create_sample_submissions(self, user):
        """Create sample form submissions."""
        try:
            from apps.forms.models import Form, FormSubmission
            
            user_forms = Form.objects.filter(created_by=user)
            
            for form in user_forms:
                # Create 2-5 submissions per form
                for i in range(2, 6):
                    submission_data = {
                        'name': f'Sample User {i}',
                        'email': f'user{i}@example.com',
                        'message': f'This is a sample submission #{i} for testing purposes.',
                    }
                    
                    submission, created = FormSubmission.objects.get_or_create(
                        form=form,
                        data=submission_data,
                        defaults={
                            'status': 'processed',
                            'created_at': timezone.now() - timedelta(days=i),
                        }
                    )
                    
                    if created:
                        # Update form submission count
                        form.submission_count += 1
                        form.save()
                        
            self.stdout.write(f'Created sample submissions for {user_forms.count()} forms')
                    
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Forms app not available, skipping submission creation')
            )
