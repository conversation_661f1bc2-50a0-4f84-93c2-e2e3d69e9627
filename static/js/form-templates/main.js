// Form Templates main entry point
import '../main.js' // Import base styles and setup
import { createApp } from 'vue'
import FormTemplates from './components/FormTemplates.vue'

// Initialize the form templates application
document.addEventListener('DOMContentLoaded', () => {
  const formTemplatesElement = document.getElementById('form-templates-app')
  
  if (formTemplatesElement) {
    const app = createApp(FormTemplates)
    
    // Get API base URL from the element's data attributes
    const apiBaseUrl = formTemplatesElement.dataset.apiBaseUrl || '/forms/api'
    
    // Provide configuration
    app.provide('config', {
      apiBaseUrl
    })
    
    app.mount('#form-templates-app')
    
    console.log('Form Templates initialized', { apiBaseUrl })
  }
})
