<template>
  <div class="form-templates">
    <!-- Loading State -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div v-for="i in 4" :key="i" class="border border-gray-200 rounded-lg p-4">
        <div class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div class="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
          <div class="h-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center py-8">
      <div class="text-red-500 mb-2">
        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <p class="text-gray-600">{{ error }}</p>
      <button @click="loadTemplates" class="mt-4 btn btn-primary">Try Again</button>
    </div>

    <!-- Templates Grid -->
    <div v-else-if="templates.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="border border-gray-200 rounded-lg p-4 hover:border-primary-300 hover:shadow-sm transition-all duration-200"
      >
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <h4 class="text-sm font-medium text-gray-900 truncate">{{ template.name }}</h4>
            <p class="text-xs text-gray-500 mt-1">{{ template.category_display }}</p>
            <p v-if="template.description" class="text-sm text-gray-600 mt-2 line-clamp-2">{{ template.description }}</p>
            <div class="flex items-center justify-between mt-3">
              <span class="text-xs text-gray-400">Used {{ template.usage_count }} times</span>
              <button 
                @click="useTemplate(template)"
                :disabled="creating"
                class="btn btn-sm btn-primary"
              >
                <span v-if="creating && selectedTemplate?.id === template.id">Creating...</span>
                <span v-else>Use Template</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-8">
      <div class="text-gray-400 mb-2">
        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <p class="text-gray-600">No templates available</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, inject } from 'vue'
import { axios } from '../../main.js'

export default {
  name: 'FormTemplates',
  setup() {
    const config = inject('config')
    
    const templates = ref([])
    const loading = ref(false)
    const error = ref(null)
    const creating = ref(false)
    const selectedTemplate = ref(null)
    
    const loadTemplates = async () => {
      loading.value = true
      error.value = null
      
      try {
        const response = await axios.get(`${config.apiBaseUrl}/templates/`)
        templates.value = response.data.results || response.data
      } catch (err) {
        error.value = err.response?.data?.detail || 'Failed to load templates'
        console.error('Error loading templates:', err)
      } finally {
        loading.value = false
      }
    }
    
    const useTemplate = async (template) => {
      creating.value = true
      selectedTemplate.value = template
      error.value = null
      
      try {
        const response = await axios.post(`${config.apiBaseUrl}/templates/${template.id}/use_template/`)
        
        // Redirect to form builder
        window.location.href = `/forms/${response.data.slug}/builder/`
        
      } catch (err) {
        error.value = err.response?.data?.detail || 'Failed to create form from template'
        console.error('Error using template:', err)
      } finally {
        creating.value = false
        selectedTemplate.value = null
      }
    }
    
    onMounted(() => {
      loadTemplates()
    })
    
    return {
      templates,
      loading,
      error,
      creating,
      selectedTemplate,
      loadTemplates,
      useTemplate
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
