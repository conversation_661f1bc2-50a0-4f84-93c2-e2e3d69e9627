<template>
  <div class="onboarding-tour" v-if="isActive">
    <!-- Tour Overlay -->
    <div class="tour-overlay" :style="overlayStyle"></div>
    
    <!-- Spotlight -->
    <div 
      class="tour-spotlight" 
      :style="spotlightStyle"
      v-if="currentStep && currentStep.target"
    ></div>
    
    <!-- Tour Tooltip -->
    <div 
      class="tour-tooltip"
      :style="tooltipStyle"
      v-if="currentStep"
    >
      <div class="tooltip-header">
        <div class="step-indicator">
          <span class="step-number">{{ currentStepIndex + 1 }}</span>
          <span class="step-total">of {{ totalSteps }}</span>
        </div>
        <button @click="closeTour" class="close-btn">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <div class="tooltip-content">
        <h3 class="tooltip-title">{{ currentStep.title }}</h3>
        <p class="tooltip-description">{{ currentStep.description }}</p>
        
        <!-- Interactive Elements -->
        <div v-if="currentStep.interactive" class="interactive-section">
          <div v-if="currentStep.interactive.type === 'try-action'" class="try-action">
            <p class="try-text">{{ currentStep.interactive.instruction }}</p>
            <button 
              @click="performAction(currentStep.interactive.action)"
              class="try-btn"
            >
              {{ currentStep.interactive.buttonText }}
            </button>
          </div>
          
          <div v-if="currentStep.interactive.type === 'input'" class="try-input">
            <label class="input-label">{{ currentStep.interactive.label }}</label>
            <input 
              v-model="interactiveInput"
              :type="currentStep.interactive.inputType || 'text'"
              :placeholder="currentStep.interactive.placeholder"
              class="input-field"
              @input="validateInput"
            />
            <p v-if="inputError" class="input-error">{{ inputError }}</p>
          </div>
        </div>
        
        <!-- Tips -->
        <div v-if="currentStep.tips" class="tips-section">
          <h4 class="tips-title">💡 Pro Tips:</h4>
          <ul class="tips-list">
            <li v-for="tip in currentStep.tips" :key="tip" class="tip-item">{{ tip }}</li>
          </ul>
        </div>
        
        <!-- Keyboard Shortcut -->
        <div v-if="currentStep.shortcut" class="shortcut-section">
          <span class="shortcut-label">Keyboard shortcut:</span>
          <kbd class="shortcut-key">{{ currentStep.shortcut }}</kbd>
        </div>
      </div>
      
      <div class="tooltip-footer">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        
        <div class="navigation-buttons">
          <button 
            @click="previousStep"
            :disabled="currentStepIndex === 0"
            class="nav-btn nav-btn-secondary"
          >
            Previous
          </button>
          
          <button 
            @click="skipTour"
            class="nav-btn nav-btn-ghost"
          >
            Skip Tour
          </button>
          
          <button 
            @click="nextStep"
            :disabled="!canProceed"
            class="nav-btn nav-btn-primary"
          >
            {{ isLastStep ? 'Finish' : 'Next' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Tour Progress Indicator -->
    <div class="tour-progress-indicator">
      <div class="progress-dots">
        <div 
          v-for="(step, index) in tourSteps"
          :key="index"
          class="progress-dot"
          :class="{
            'completed': index < currentStepIndex,
            'active': index === currentStepIndex,
            'upcoming': index > currentStepIndex
          }"
          @click="goToStep(index)"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

export default {
  name: 'OnboardingTour',
  props: {
    steps: {
      type: Array,
      required: true
    },
    autoStart: {
      type: Boolean,
      default: false
    }
  },
  emits: ['tour-completed', 'tour-skipped', 'step-changed'],
  setup(props, { emit }) {
    const isActive = ref(false)
    const currentStepIndex = ref(0)
    const interactiveInput = ref('')
    const inputError = ref('')
    const targetElement = ref(null)
    const tooltipPosition = ref({ top: 0, left: 0 })
    
    const tourSteps = ref(props.steps)
    
    const currentStep = computed(() => {
      return tourSteps.value[currentStepIndex.value]
    })
    
    const totalSteps = computed(() => tourSteps.value.length)
    
    const progressPercentage = computed(() => {
      return ((currentStepIndex.value + 1) / totalSteps.value) * 100
    })
    
    const isLastStep = computed(() => {
      return currentStepIndex.value === totalSteps.value - 1
    })
    
    const canProceed = computed(() => {
      if (!currentStep.value.interactive) return true
      
      if (currentStep.value.interactive.type === 'input') {
        return interactiveInput.value && !inputError.value
      }
      
      return true
    })
    
    const overlayStyle = computed(() => {
      if (!targetElement.value) return {}
      
      const rect = targetElement.value.getBoundingClientRect()
      return {
        clipPath: `polygon(0% 0%, 0% 100%, ${rect.left}px 100%, ${rect.left}px ${rect.top}px, ${rect.right}px ${rect.top}px, ${rect.right}px ${rect.bottom}px, ${rect.left}px ${rect.bottom}px, ${rect.left}px 100%, 100% 100%, 100% 0%)`
      }
    })
    
    const spotlightStyle = computed(() => {
      if (!targetElement.value) return {}
      
      const rect = targetElement.value.getBoundingClientRect()
      return {
        top: `${rect.top - 4}px`,
        left: `${rect.left - 4}px`,
        width: `${rect.width + 8}px`,
        height: `${rect.height + 8}px`
      }
    })
    
    const tooltipStyle = computed(() => {
      return {
        top: `${tooltipPosition.value.top}px`,
        left: `${tooltipPosition.value.left}px`
      }
    })
    
    const startTour = () => {
      isActive.value = true
      currentStepIndex.value = 0
      updateTargetElement()
    }
    
    const closeTour = () => {
      isActive.value = false
      emit('tour-completed', currentStepIndex.value === totalSteps.value - 1)
    }
    
    const skipTour = () => {
      isActive.value = false
      emit('tour-skipped', currentStepIndex.value)
    }
    
    const nextStep = () => {
      if (isLastStep.value) {
        closeTour()
      } else {
        currentStepIndex.value++
        updateTargetElement()
        resetInteractiveState()
        emit('step-changed', currentStepIndex.value)
      }
    }
    
    const previousStep = () => {
      if (currentStepIndex.value > 0) {
        currentStepIndex.value--
        updateTargetElement()
        resetInteractiveState()
        emit('step-changed', currentStepIndex.value)
      }
    }
    
    const goToStep = (stepIndex) => {
      if (stepIndex >= 0 && stepIndex < totalSteps.value) {
        currentStepIndex.value = stepIndex
        updateTargetElement()
        resetInteractiveState()
        emit('step-changed', currentStepIndex.value)
      }
    }
    
    const updateTargetElement = async () => {
      await nextTick()
      
      if (currentStep.value.target) {
        const element = document.querySelector(currentStep.value.target)
        if (element) {
          targetElement.value = element
          
          // Scroll element into view
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
          })
          
          // Add highlight class
          element.classList.add('tour-highlight')
          
          // Calculate tooltip position
          calculateTooltipPosition(element)
        }
      } else {
        targetElement.value = null
        calculateTooltipPosition()
      }
    }
    
    const calculateTooltipPosition = (element = null) => {
      const tooltip = document.querySelector('.tour-tooltip')
      if (!tooltip) return
      
      const tooltipRect = tooltip.getBoundingClientRect()
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      
      let top, left
      
      if (element) {
        const rect = element.getBoundingClientRect()
        
        // Position tooltip based on available space
        if (rect.bottom + tooltipRect.height + 20 < viewportHeight) {
          // Below element
          top = rect.bottom + 20
        } else if (rect.top - tooltipRect.height - 20 > 0) {
          // Above element
          top = rect.top - tooltipRect.height - 20
        } else {
          // Center vertically
          top = (viewportHeight - tooltipRect.height) / 2
        }
        
        // Center horizontally relative to element
        left = rect.left + (rect.width - tooltipRect.width) / 2
        
        // Ensure tooltip stays within viewport
        if (left < 20) left = 20
        if (left + tooltipRect.width > viewportWidth - 20) {
          left = viewportWidth - tooltipRect.width - 20
        }
      } else {
        // Center in viewport
        top = (viewportHeight - tooltipRect.height) / 2
        left = (viewportWidth - tooltipRect.width) / 2
      }
      
      tooltipPosition.value = { top, left }
    }
    
    const resetInteractiveState = () => {
      interactiveInput.value = ''
      inputError.value = ''
    }
    
    const performAction = (action) => {
      // Emit action to parent component
      emit('perform-action', action)
    }
    
    const validateInput = () => {
      if (!currentStep.value.interactive?.validation) return
      
      const validation = currentStep.value.interactive.validation
      const value = interactiveInput.value
      
      if (validation.required && !value) {
        inputError.value = 'This field is required'
        return
      }
      
      if (validation.minLength && value.length < validation.minLength) {
        inputError.value = `Must be at least ${validation.minLength} characters`
        return
      }
      
      if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
        inputError.value = validation.message || 'Invalid format'
        return
      }
      
      inputError.value = ''
    }
    
    // Handle window resize
    const handleResize = () => {
      if (targetElement.value) {
        calculateTooltipPosition(targetElement.value)
      }
    }
    
    // Handle escape key
    const handleKeydown = (event) => {
      if (event.key === 'Escape' && isActive.value) {
        closeTour()
      }
    }
    
    onMounted(() => {
      if (props.autoStart) {
        startTour()
      }
      
      window.addEventListener('resize', handleResize)
      document.addEventListener('keydown', handleKeydown)
    })
    
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      document.removeEventListener('keydown', handleKeydown)
      
      // Remove highlight classes
      document.querySelectorAll('.tour-highlight').forEach(el => {
        el.classList.remove('tour-highlight')
      })
    })
    
    return {
      isActive,
      currentStepIndex,
      interactiveInput,
      inputError,
      tourSteps,
      currentStep,
      totalSteps,
      progressPercentage,
      isLastStep,
      canProceed,
      overlayStyle,
      spotlightStyle,
      tooltipStyle,
      startTour,
      closeTour,
      skipTour,
      nextStep,
      previousStep,
      goToStep,
      performAction,
      validateInput
    }
  }
}
</script>

<style scoped>
.onboarding-tour {
  @apply fixed inset-0 z-50;
}

.tour-overlay {
  @apply absolute inset-0 bg-black bg-opacity-50 transition-all duration-300;
}

.tour-spotlight {
  @apply absolute border-2 border-primary-400 rounded-lg shadow-lg transition-all duration-300;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.tour-tooltip {
  @apply absolute bg-white rounded-lg shadow-xl border border-gray-200 max-w-sm w-80 transition-all duration-300;
}

.tooltip-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.step-indicator {
  @apply text-sm font-medium text-gray-600;
}

.step-number {
  @apply text-primary-600 font-semibold;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.tooltip-content {
  @apply p-4 space-y-3;
}

.tooltip-title {
  @apply text-lg font-semibold text-gray-900;
}

.tooltip-description {
  @apply text-sm text-gray-600 leading-relaxed;
}

.interactive-section {
  @apply bg-blue-50 p-3 rounded-lg;
}

.try-action {
  @apply space-y-2;
}

.try-text {
  @apply text-sm text-blue-800;
}

.try-btn {
  @apply px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors;
}

.try-input {
  @apply space-y-2;
}

.input-label {
  @apply block text-sm font-medium text-blue-800;
}

.input-field {
  @apply w-full px-3 py-2 text-sm border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.input-error {
  @apply text-xs text-red-600;
}

.tips-section {
  @apply bg-yellow-50 p-3 rounded-lg;
}

.tips-title {
  @apply text-sm font-medium text-yellow-800 mb-2;
}

.tips-list {
  @apply space-y-1;
}

.tip-item {
  @apply text-sm text-yellow-700;
}

.shortcut-section {
  @apply flex items-center space-x-2 text-sm;
}

.shortcut-label {
  @apply text-gray-600;
}

.shortcut-key {
  @apply px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded;
}

.tooltip-footer {
  @apply p-4 border-t border-gray-200 space-y-3;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary-500 transition-all duration-300;
}

.navigation-buttons {
  @apply flex items-center justify-between;
}

.nav-btn {
  @apply px-4 py-2 text-sm font-medium rounded-lg transition-colors;
}

.nav-btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.nav-btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.nav-btn-ghost {
  @apply text-gray-500 hover:text-gray-700;
}

.tour-progress-indicator {
  @apply fixed bottom-6 left-1/2 transform -translate-x-1/2;
}

.progress-dots {
  @apply flex items-center space-x-2 bg-white rounded-full px-4 py-2 shadow-lg;
}

.progress-dot {
  @apply w-3 h-3 rounded-full cursor-pointer transition-all duration-200;
}

.progress-dot.completed {
  @apply bg-green-500;
}

.progress-dot.active {
  @apply bg-primary-500 scale-125;
}

.progress-dot.upcoming {
  @apply bg-gray-300;
}

/* Global tour highlight style */
:global(.tour-highlight) {
  position: relative;
  z-index: 51;
}

:global(.tour-highlight::after) {
  content: '';
  position: absolute;
  inset: -4px;
  border: 2px solid #3B82F6;
  border-radius: 8px;
  pointer-events: none;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}
</style>
