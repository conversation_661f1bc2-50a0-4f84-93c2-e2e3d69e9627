<template>
  <div class="enhanced-form-canvas">
    <!-- Toolbar -->
    <div class="canvas-toolbar">
      <div class="toolbar-left">
        <button 
          @click="selectAllFields"
          class="toolbar-btn"
          :disabled="fields.length === 0"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Select All
        </button>
        
        <button 
          @click="deleteSelectedFields"
          class="toolbar-btn toolbar-btn-danger"
          :disabled="selectedFields.length === 0"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Delete Selected ({{ selectedFields.length }})
        </button>
        
        <button 
          @click="duplicateSelectedFields"
          class="toolbar-btn"
          :disabled="selectedFields.length === 0"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
          Duplicate Selected
        </button>
      </div>
      
      <div class="toolbar-right">
        <div class="field-count">
          {{ fields.length }} field{{ fields.length !== 1 ? 's' : '' }}
        </div>
      </div>
    </div>

    <!-- Canvas Area -->
    <div class="canvas-area">
      <!-- Empty state -->
      <div 
        v-if="fields.length === 0"
        class="empty-canvas"
        :class="{ 'drag-over': isDragOver }"
        @drop="onDrop"
        @dragover.prevent="handleDragOver"
        @dragenter.prevent="handleDragEnter"
        @dragleave="handleDragLeave"
      >
        <div class="empty-content">
          <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          <h3 class="empty-title">Start Building Your Form</h3>
          <p class="empty-description">
            Drag fields from the sidebar to create your form
          </p>
          <div v-if="isDragOver" class="drag-indicator">
            <div class="drag-line"></div>
            <span class="drag-text">Drop field here</span>
          </div>
        </div>
      </div>
      
      <!-- Fields Container -->
      <div 
        v-else
        ref="sortableContainer"
        class="fields-container"
        :class="{ 'drag-over': isDragOver }"
        @drop="onDrop"
        @dragover.prevent="handleDragOver"
        @dragenter.prevent="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="clearSelection"
      >
        <!-- Drop zone at start -->
        <DropZone 
          position="start"
          :active="dropZoneActive === 'start'"
          @drop="onDropAtPosition($event, 0)"
          @activate="activateDropZone('start')"
          @deactivate="deactivateDropZone"
        />

        <template v-for="(field, index) in fields" :key="field.id">
          <EnhancedFormField
            :field="field"
            :index="index"
            :is-selected="isFieldSelected(field.id)"
            :is-dragging="draggingField === field.id"
            :multi-select="multiSelectMode"
            @field-selected="handleFieldSelection"
            @field-updated="updateField"
            @field-deleted="deleteField"
            @field-duplicated="duplicateField"
            @drag-start="handleFieldDragStart"
            @drag-end="handleFieldDragEnd"
          />
          
          <!-- Drop zone between fields -->
          <DropZone 
            :position="`between-${index}`"
            :active="dropZoneActive === `between-${index}`"
            @drop="onDropAtPosition($event, index + 1)"
            @activate="activateDropZone(`between-${index}`)"
            @deactivate="deactivateDropZone"
          />
        </template>
        
        <!-- Drop zone at end -->
        <DropZone 
          position="end"
          :active="dropZoneActive === 'end'"
          @drop="onDropAtPosition($event, fields.length)"
          @activate="activateDropZone('end')"
          @deactivate="deactivateDropZone"
          text="Drop field here to add at the end"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import Sortable from 'sortablejs'
import EnhancedFormField from './EnhancedFormField.vue'
import DropZone from './DropZone.vue'

export default {
  name: 'EnhancedFormCanvas',
  components: {
    EnhancedFormField,
    DropZone
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    selectedField: {
      type: Object,
      default: null
    }
  },
  emits: [
    'field-selected', 
    'field-updated', 
    'field-deleted', 
    'field-duplicated',
    'fields-reordered',
    'bulk-delete',
    'bulk-duplicate'
  ],
  setup(props, { emit }) {
    const sortableContainer = ref(null)
    let sortableInstance = null
    
    // State
    const isDragOver = ref(false)
    const dropZoneActive = ref(null)
    const draggingField = ref(null)
    const dragCounter = ref(0)
    const selectedFields = ref([])
    const multiSelectMode = ref(false)
    
    // Computed
    const hasSelection = computed(() => selectedFields.value.length > 0)
    
    // Field selection
    const isFieldSelected = (fieldId) => {
      return selectedFields.value.includes(fieldId)
    }
    
    const handleFieldSelection = (field, event) => {
      if (event?.ctrlKey || event?.metaKey) {
        // Multi-select mode
        multiSelectMode.value = true
        const index = selectedFields.value.indexOf(field.id)
        if (index > -1) {
          selectedFields.value.splice(index, 1)
        } else {
          selectedFields.value.push(field.id)
        }
      } else if (event?.shiftKey && selectedFields.value.length > 0) {
        // Range select
        const lastSelected = selectedFields.value[selectedFields.value.length - 1]
        const lastIndex = props.fields.findIndex(f => f.id === lastSelected)
        const currentIndex = props.fields.findIndex(f => f.id === field.id)
        
        const start = Math.min(lastIndex, currentIndex)
        const end = Math.max(lastIndex, currentIndex)
        
        selectedFields.value = props.fields
          .slice(start, end + 1)
          .map(f => f.id)
      } else {
        // Single select
        multiSelectMode.value = false
        selectedFields.value = [field.id]
        emit('field-selected', field)
      }
    }
    
    const clearSelection = () => {
      selectedFields.value = []
      multiSelectMode.value = false
    }
    
    const selectAllFields = () => {
      selectedFields.value = props.fields.map(f => f.id)
      multiSelectMode.value = true
    }
    
    // Bulk operations
    const deleteSelectedFields = () => {
      emit('bulk-delete', selectedFields.value)
      selectedFields.value = []
    }
    
    const duplicateSelectedFields = () => {
      emit('bulk-duplicate', selectedFields.value)
    }
    
    // Field operations
    const updateField = (fieldData) => {
      emit('field-updated', fieldData)
    }
    
    const deleteField = (fieldId) => {
      emit('field-deleted', fieldId)
      selectedFields.value = selectedFields.value.filter(id => id !== fieldId)
    }
    
    const duplicateField = (field) => {
      emit('field-duplicated', field)
    }
    
    // Drag and drop
    const handleDragOver = (event) => {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    }
    
    const handleDragEnter = (event) => {
      event.preventDefault()
      dragCounter.value++
      if (dragCounter.value === 1) {
        isDragOver.value = true
      }
    }
    
    const handleDragLeave = (event) => {
      dragCounter.value--
      if (dragCounter.value === 0) {
        isDragOver.value = false
        dropZoneActive.value = null
      }
    }
    
    const activateDropZone = (zone) => {
      dropZoneActive.value = zone
    }
    
    const deactivateDropZone = () => {
      dropZoneActive.value = null
    }
    
    const handleFieldDragStart = (fieldId) => {
      draggingField.value = fieldId
    }
    
    const handleFieldDragEnd = () => {
      draggingField.value = null
      isDragOver.value = false
      dropZoneActive.value = null
      dragCounter.value = 0
    }
    
    const onDrop = (event) => {
      event.preventDefault()
      const fieldType = event.dataTransfer.getData('text/plain')
      
      if (fieldType) {
        emit('field-added', fieldType, 0)
      }
      
      resetDragState()
    }
    
    const onDropAtPosition = (event, position) => {
      event.preventDefault()
      const fieldType = event.dataTransfer.getData('text/plain')
      
      if (fieldType) {
        emit('field-added', fieldType, position)
      }
      
      resetDragState()
    }
    
    const resetDragState = () => {
      isDragOver.value = false
      dropZoneActive.value = null
      dragCounter.value = 0
    }
    
    return {
      sortableContainer,
      isDragOver,
      dropZoneActive,
      draggingField,
      selectedFields,
      multiSelectMode,
      hasSelection,
      isFieldSelected,
      handleFieldSelection,
      clearSelection,
      selectAllFields,
      deleteSelectedFields,
      duplicateSelectedFields,
      updateField,
      deleteField,
      duplicateField,
      handleDragOver,
      handleDragEnter,
      handleDragLeave,
      activateDropZone,
      deactivateDropZone,
      handleFieldDragStart,
      handleFieldDragEnd,
      onDrop,
      onDropAtPosition
    }
  }
}
</script>

<style scoped>
.enhanced-form-canvas {
  @apply flex flex-col h-full;
}

.canvas-toolbar {
  @apply flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200;
}

.toolbar-left {
  @apply flex items-center space-x-2;
}

.toolbar-right {
  @apply flex items-center space-x-2;
}

.toolbar-btn {
  @apply flex items-center space-x-1 px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
}

.toolbar-btn-danger {
  @apply text-red-600 border-red-300 hover:bg-red-50;
}

.field-count {
  @apply text-sm text-gray-500;
}

.canvas-area {
  @apply flex-1 overflow-y-auto;
}

.empty-canvas {
  @apply h-full flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg m-4 transition-colors;
}

.empty-canvas.drag-over {
  @apply border-primary-500 bg-primary-50;
}

.empty-content {
  @apply text-center;
}

.empty-icon {
  @apply w-12 h-12 mx-auto text-gray-400 mb-4;
}

.empty-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.empty-description {
  @apply text-gray-500 mb-4;
}

.drag-indicator {
  @apply flex items-center justify-center space-x-2 text-primary-600;
}

.drag-line {
  @apply w-8 h-0.5 bg-primary-500;
}

.drag-text {
  @apply text-sm font-medium;
}

.fields-container {
  @apply p-4 space-y-2;
}

.fields-container.drag-over {
  @apply bg-blue-50;
}
</style>
