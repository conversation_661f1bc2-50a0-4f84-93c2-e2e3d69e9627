<template>
  <div class="form-settings-panel">
    <div class="panel-header">
      <h3 class="panel-title">Form Settings</h3>
      <button @click="resetSettings" class="reset-btn">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Reset
      </button>
    </div>

    <div class="panel-content">
      <!-- Basic Settings -->
      <div class="settings-section">
        <h4 class="section-title">Basic Information</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="setting-label">Form Title</label>
            <input 
              v-model="localSettings.title"
              @input="updateSettings"
              type="text" 
              class="setting-input"
              placeholder="Enter form title"
            />
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Description</label>
            <textarea 
              v-model="localSettings.description"
              @input="updateSettings"
              class="setting-textarea"
              rows="3"
              placeholder="Describe your form"
            ></textarea>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Submit Button Text</label>
            <input 
              v-model="localSettings.submit_button_text"
              @input="updateSettings"
              type="text" 
              class="setting-input"
              placeholder="Submit"
            />
          </div>
        </div>
      </div>

      <!-- Submission Settings -->
      <div class="settings-section">
        <h4 class="section-title">Submission Settings</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="setting-label">Submission Handling</label>
            <select 
              v-model="localSettings.submission_handling"
              @change="updateSettings"
              class="setting-select"
            >
              <option value="database">Store in Database</option>
              <option value="email">Email Notifications</option>
              <option value="webhook">Webhook Integration</option>
              <option value="pdf">PDF Generation</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Success Message</label>
            <textarea 
              v-model="localSettings.success_message"
              @input="updateSettings"
              class="setting-textarea"
              rows="2"
              placeholder="Thank you for your submission!"
            ></textarea>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Redirect URL (Optional)</label>
            <input 
              v-model="localSettings.redirect_url"
              @input="updateSettings"
              type="url" 
              class="setting-input"
              placeholder="https://example.com/thank-you"
            />
          </div>
        </div>
      </div>

      <!-- Access Control -->
      <div class="settings-section">
        <h4 class="section-title">Access Control</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.is_public"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Make form publicly accessible
            </label>
          </div>
          
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.requires_authentication"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Require user authentication
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Maximum Submissions</label>
            <input 
              v-model="localSettings.max_submissions"
              @input="updateSettings"
              type="number" 
              class="setting-input"
              placeholder="Unlimited"
              min="1"
            />
          </div>
        </div>
      </div>

      <!-- Email Notifications -->
      <div class="settings-section">
        <h4 class="section-title">Email Notifications</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.send_confirmation_email"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Send confirmation email to submitter
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Notification Email</label>
            <input 
              v-model="localSettings.notification_email"
              @input="updateSettings"
              type="email" 
              class="setting-input"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Email Subject</label>
            <input 
              v-model="localSettings.email_subject"
              @input="updateSettings"
              type="text" 
              class="setting-input"
              placeholder="New form submission"
            />
          </div>
        </div>
      </div>

      <!-- Advanced Settings -->
      <div class="settings-section">
        <h4 class="section-title">Advanced Settings</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.allow_multiple_submissions"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Allow multiple submissions per user
            </label>
          </div>
          
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.save_progress"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Allow users to save progress
            </label>
          </div>
          
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.show_progress_bar"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Show progress bar (multi-page forms)
            </label>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Form Expiry Date</label>
            <input 
              v-model="localSettings.expiry_date"
              @input="updateSettings"
              type="datetime-local" 
              class="setting-input"
            />
          </div>
        </div>
      </div>

      <!-- SEO Settings -->
      <div class="settings-section">
        <h4 class="section-title">SEO & Meta</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="setting-label">Meta Title</label>
            <input 
              v-model="localSettings.meta_title"
              @input="updateSettings"
              type="text" 
              class="setting-input"
              placeholder="Form title for search engines"
            />
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Meta Description</label>
            <textarea 
              v-model="localSettings.meta_description"
              @input="updateSettings"
              class="setting-textarea"
              rows="2"
              placeholder="Brief description for search engines"
            ></textarea>
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Custom Slug</label>
            <input 
              v-model="localSettings.custom_slug"
              @input="updateSettings"
              type="text" 
              class="setting-input"
              placeholder="custom-form-url"
            />
          </div>
        </div>
      </div>

      <!-- Integration Settings -->
      <div class="settings-section">
        <h4 class="section-title">Integrations</h4>
        <div class="settings-grid">
          <div class="setting-group">
            <label class="setting-label">Webhook URL</label>
            <input 
              v-model="localSettings.webhook_url"
              @input="updateSettings"
              type="url" 
              class="setting-input"
              placeholder="https://api.example.com/webhook"
            />
          </div>
          
          <div class="setting-group">
            <label class="setting-label">Google Analytics ID</label>
            <input 
              v-model="localSettings.analytics_id"
              @input="updateSettings"
              type="text" 
              class="setting-input"
              placeholder="GA-XXXXXXXXX-X"
            />
          </div>
          
          <div class="setting-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="localSettings.enable_captcha"
                @change="updateSettings"
                class="setting-checkbox"
              />
              Enable CAPTCHA protection
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'

export default {
  name: 'FormSettingsPanel',
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  emits: ['settings-updated'],
  setup(props, { emit }) {
    const localSettings = ref({
      title: '',
      description: '',
      submit_button_text: 'Submit',
      submission_handling: 'database',
      success_message: 'Thank you for your submission!',
      redirect_url: '',
      is_public: true,
      requires_authentication: false,
      max_submissions: null,
      send_confirmation_email: false,
      notification_email: '',
      email_subject: 'New form submission',
      allow_multiple_submissions: true,
      save_progress: false,
      show_progress_bar: true,
      expiry_date: '',
      meta_title: '',
      meta_description: '',
      custom_slug: '',
      webhook_url: '',
      analytics_id: '',
      enable_captcha: false,
      ...props.form
    })

    const updateSettings = () => {
      emit('settings-updated', { ...localSettings.value })
    }

    const resetSettings = () => {
      localSettings.value = {
        title: '',
        description: '',
        submit_button_text: 'Submit',
        submission_handling: 'database',
        success_message: 'Thank you for your submission!',
        redirect_url: '',
        is_public: true,
        requires_authentication: false,
        max_submissions: null,
        send_confirmation_email: false,
        notification_email: '',
        email_subject: 'New form submission',
        allow_multiple_submissions: true,
        save_progress: false,
        show_progress_bar: true,
        expiry_date: '',
        meta_title: '',
        meta_description: '',
        custom_slug: '',
        webhook_url: '',
        analytics_id: '',
        enable_captcha: false
      }
      updateSettings()
    }

    // Watch for changes in form prop
    watch(() => props.form, (newForm) => {
      Object.assign(localSettings.value, newForm)
    }, { deep: true })

    return {
      localSettings,
      updateSettings,
      resetSettings
    }
  }
}
</script>

<style scoped>
.form-settings-panel {
  @apply h-full flex flex-col;
}

.panel-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.panel-title {
  @apply text-lg font-semibold text-gray-900;
}

.reset-btn {
  @apply flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.panel-content {
  @apply flex-1 overflow-y-auto p-4 space-y-6;
}

.settings-section {
  @apply space-y-4;
}

.section-title {
  @apply text-sm font-medium text-gray-900 pb-2 border-b border-gray-200;
}

.settings-grid {
  @apply space-y-4;
}

.setting-group {
  @apply space-y-2;
}

.setting-label {
  @apply block text-sm font-medium text-gray-700;
}

.checkbox-label {
  @apply flex items-center space-x-2 text-sm text-gray-700 cursor-pointer;
}

.setting-input,
.setting-textarea,
.setting-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.setting-textarea {
  @apply resize-none;
}

.setting-checkbox {
  @apply form-checkbox text-primary-600 focus:ring-primary-500;
}
</style>
