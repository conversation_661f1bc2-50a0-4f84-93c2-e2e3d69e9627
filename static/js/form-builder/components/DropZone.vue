<template>
  <div 
    class="drop-zone"
    :class="{ 
      'active': active,
      'between': position.startsWith('between'),
      'start': position === 'start',
      'end': position === 'end'
    }"
    @drop="handleDrop"
    @dragover.prevent="handleDragOver"
    @dragenter.prevent="handleDragEnter"
    @dragleave="handleDragLeave"
  >
    <div class="drop-zone-indicator">
      <div class="drop-zone-line"></div>
      <span class="drop-zone-text">{{ displayText }}</span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'DropZone',
  props: {
    position: {
      type: String,
      required: true
    },
    active: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: 'Drop here'
    }
  },
  emits: ['drop', 'activate', 'deactivate'],
  setup(props, { emit }) {
    const displayText = computed(() => {
      if (props.text !== 'Drop here') {
        return props.text
      }
      
      if (props.position === 'start') {
        return 'Drop at beginning'
      } else if (props.position === 'end') {
        return 'Drop at end'
      } else if (props.position.startsWith('between')) {
        return 'Drop here'
      }
      
      return 'Drop here'
    })
    
    const handleDrop = (event) => {
      emit('drop', event)
      emit('deactivate')
    }
    
    const handleDragOver = (event) => {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
      emit('activate')
    }
    
    const handleDragEnter = (event) => {
      event.preventDefault()
      emit('activate')
    }
    
    const handleDragLeave = (event) => {
      // Only deactivate if we're leaving the drop zone entirely
      if (!event.currentTarget.contains(event.relatedTarget)) {
        emit('deactivate')
      }
    }
    
    return {
      displayText,
      handleDrop,
      handleDragOver,
      handleDragEnter,
      handleDragLeave
    }
  }
}
</script>

<style scoped>
.drop-zone {
  @apply relative opacity-0 transition-all duration-200;
}

.drop-zone.between {
  @apply h-2 -my-1;
}

.drop-zone.start,
.drop-zone.end {
  @apply h-8 my-2;
}

.drop-zone.active {
  @apply opacity-100;
}

.drop-zone-indicator {
  @apply flex items-center justify-center h-full;
}

.drop-zone-line {
  @apply flex-1 h-0.5 bg-primary-500 rounded;
}

.drop-zone.between .drop-zone-line {
  @apply h-0.5;
}

.drop-zone.start .drop-zone-line,
.drop-zone.end .drop-zone-line {
  @apply h-px;
}

.drop-zone-text {
  @apply px-3 text-xs font-medium text-primary-600 bg-white rounded-full border border-primary-200;
}

.drop-zone.between .drop-zone-text {
  @apply px-2 py-0.5;
}

/* Hover effects */
.drop-zone:hover {
  @apply opacity-60;
}

.drop-zone.active:hover {
  @apply opacity-100;
}

/* Animation for smooth appearance */
.drop-zone.active .drop-zone-indicator {
  @apply animate-pulse;
}
</style>
