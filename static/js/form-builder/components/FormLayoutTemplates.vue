<template>
  <div class="form-layout-templates">
    <div class="templates-header">
      <h4 class="templates-title">Layout Templates</h4>
      <button @click="createCustomTemplate" class="create-template-btn">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Create Template
      </button>
    </div>

    <div class="templates-grid">
      <div 
        v-for="template in layoutTemplates" 
        :key="template.id"
        class="template-card"
        :class="{ 'selected': selectedTemplate === template.id }"
        @click="selectTemplate(template)"
      >
        <div class="template-preview">
          <div class="preview-container" :style="getPreviewStyle(template)">
            <div 
              v-for="(section, index) in template.sections" 
              :key="index"
              class="preview-section"
              :style="getSectionStyle(section)"
            >
              <div 
                v-for="(field, fieldIndex) in section.fields" 
                :key="fieldIndex"
                class="preview-field"
                :style="getFieldStyle(field)"
              ></div>
            </div>
          </div>
        </div>
        
        <div class="template-info">
          <h5 class="template-name">{{ template.name }}</h5>
          <p class="template-description">{{ template.description }}</p>
          <div class="template-meta">
            <span class="field-count">{{ getFieldCount(template) }} fields</span>
            <span class="usage-count">Used {{ template.usageCount || 0 }} times</span>
          </div>
        </div>
        
        <div class="template-actions">
          <button 
            @click.stop="previewTemplate(template)"
            class="action-btn"
            title="Preview template"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </button>
          
          <button 
            @click.stop="editTemplate(template)"
            class="action-btn"
            title="Edit template"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
          </button>
          
          <button 
            v-if="template.isCustom"
            @click.stop="deleteTemplate(template)"
            class="action-btn action-btn-danger"
            title="Delete template"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Template Editor Modal -->
    <div v-if="showTemplateEditor" class="template-editor-modal">
      <div class="modal-backdrop" @click="closeTemplateEditor"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">{{ editingTemplate ? 'Edit Template' : 'Create Template' }}</h3>
          <button @click="closeTemplateEditor" class="modal-close">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">Template Name</label>
            <input 
              v-model="templateForm.name"
              type="text" 
              class="form-input"
              placeholder="Enter template name"
            />
          </div>
          
          <div class="form-group">
            <label class="form-label">Description</label>
            <textarea 
              v-model="templateForm.description"
              class="form-textarea"
              rows="3"
              placeholder="Describe this template"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">Layout Configuration</label>
            <div class="layout-config">
              <div class="config-section">
                <h5 class="config-title">Columns</h5>
                <select v-model="templateForm.columns" class="form-select">
                  <option value="1">1 Column</option>
                  <option value="2">2 Columns</option>
                  <option value="3">3 Columns</option>
                </select>
              </div>
              
              <div class="config-section">
                <h5 class="config-title">Field Spacing</h5>
                <input 
                  v-model="templateForm.fieldSpacing"
                  type="range" 
                  min="8" 
                  max="32" 
                  step="4"
                  class="form-range"
                />
                <span class="range-value">{{ templateForm.fieldSpacing }}px</span>
              </div>
              
              <div class="config-section">
                <h5 class="config-title">Section Spacing</h5>
                <input 
                  v-model="templateForm.sectionSpacing"
                  type="range" 
                  min="16" 
                  max="64" 
                  step="8"
                  class="form-range"
                />
                <span class="range-value">{{ templateForm.sectionSpacing }}px</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="closeTemplateEditor" class="btn btn-secondary">Cancel</button>
          <button @click="saveTemplate" class="btn btn-primary">
            {{ editingTemplate ? 'Update Template' : 'Create Template' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'FormLayoutTemplates',
  props: {
    currentForm: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['template-selected', 'template-applied'],
  setup(props, { emit }) {
    const selectedTemplate = ref(null)
    const showTemplateEditor = ref(false)
    const editingTemplate = ref(null)
    
    const templateForm = ref({
      name: '',
      description: '',
      columns: '1',
      fieldSpacing: 16,
      sectionSpacing: 24
    })
    
    const layoutTemplates = ref([
      {
        id: 'single-column',
        name: 'Single Column',
        description: 'Simple single column layout',
        isCustom: false,
        usageCount: 45,
        sections: [
          {
            columns: 1,
            fields: [
              { width: '100%', height: '40px' },
              { width: '100%', height: '40px' },
              { width: '100%', height: '80px' }
            ]
          }
        ]
      },
      {
        id: 'two-column',
        name: 'Two Column',
        description: 'Side-by-side field layout',
        isCustom: false,
        usageCount: 32,
        sections: [
          {
            columns: 2,
            fields: [
              { width: '48%', height: '40px' },
              { width: '48%', height: '40px' },
              { width: '100%', height: '80px' }
            ]
          }
        ]
      },
      {
        id: 'contact-form',
        name: 'Contact Form',
        description: 'Standard contact form layout',
        isCustom: false,
        usageCount: 28,
        sections: [
          {
            columns: 2,
            fields: [
              { width: '48%', height: '40px' },
              { width: '48%', height: '40px' },
              { width: '100%', height: '40px' },
              { width: '100%', height: '80px' }
            ]
          }
        ]
      },
      {
        id: 'survey-form',
        name: 'Survey Form',
        description: 'Multi-section survey layout',
        isCustom: false,
        usageCount: 19,
        sections: [
          {
            columns: 1,
            fields: [
              { width: '100%', height: '40px' },
              { width: '100%', height: '40px' }
            ]
          },
          {
            columns: 2,
            fields: [
              { width: '48%', height: '40px' },
              { width: '48%', height: '40px' }
            ]
          }
        ]
      }
    ])
    
    const selectTemplate = (template) => {
      selectedTemplate.value = template.id
      emit('template-selected', template)
    }
    
    const previewTemplate = (template) => {
      // Open template preview
      console.log('Preview template:', template)
    }
    
    const editTemplate = (template) => {
      editingTemplate.value = template
      templateForm.value = {
        name: template.name,
        description: template.description,
        columns: template.sections[0]?.columns?.toString() || '1',
        fieldSpacing: 16,
        sectionSpacing: 24
      }
      showTemplateEditor.value = true
    }
    
    const deleteTemplate = (template) => {
      if (confirm(`Are you sure you want to delete "${template.name}"?`)) {
        const index = layoutTemplates.value.findIndex(t => t.id === template.id)
        if (index > -1) {
          layoutTemplates.value.splice(index, 1)
        }
      }
    }
    
    const createCustomTemplate = () => {
      editingTemplate.value = null
      templateForm.value = {
        name: '',
        description: '',
        columns: '1',
        fieldSpacing: 16,
        sectionSpacing: 24
      }
      showTemplateEditor.value = true
    }
    
    const closeTemplateEditor = () => {
      showTemplateEditor.value = false
      editingTemplate.value = null
    }
    
    const saveTemplate = () => {
      const template = {
        id: editingTemplate.value?.id || `custom-${Date.now()}`,
        name: templateForm.value.name,
        description: templateForm.value.description,
        isCustom: true,
        usageCount: editingTemplate.value?.usageCount || 0,
        sections: [
          {
            columns: parseInt(templateForm.value.columns),
            fields: []
          }
        ]
      }
      
      if (editingTemplate.value) {
        const index = layoutTemplates.value.findIndex(t => t.id === editingTemplate.value.id)
        if (index > -1) {
          layoutTemplates.value[index] = template
        }
      } else {
        layoutTemplates.value.push(template)
      }
      
      closeTemplateEditor()
    }
    
    const getPreviewStyle = (template) => {
      return {
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        padding: '8px'
      }
    }
    
    const getSectionStyle = (section) => {
      return {
        display: 'grid',
        gridTemplateColumns: `repeat(${section.columns}, 1fr)`,
        gap: '4px'
      }
    }
    
    const getFieldStyle = (field) => {
      return {
        width: field.width,
        height: field.height,
        backgroundColor: '#E5E7EB',
        borderRadius: '4px'
      }
    }
    
    const getFieldCount = (template) => {
      return template.sections.reduce((total, section) => total + section.fields.length, 0)
    }
    
    return {
      selectedTemplate,
      showTemplateEditor,
      editingTemplate,
      templateForm,
      layoutTemplates,
      selectTemplate,
      previewTemplate,
      editTemplate,
      deleteTemplate,
      createCustomTemplate,
      closeTemplateEditor,
      saveTemplate,
      getPreviewStyle,
      getSectionStyle,
      getFieldStyle,
      getFieldCount
    }
  }
}
</script>

<style scoped>
.form-layout-templates {
  @apply space-y-4;
}

.templates-header {
  @apply flex items-center justify-between;
}

.templates-title {
  @apply text-sm font-medium text-gray-900;
}

.create-template-btn {
  @apply flex items-center space-x-1 px-3 py-1 text-sm text-primary-600 border border-primary-300 rounded hover:bg-primary-50 transition-colors;
}

.templates-grid {
  @apply grid grid-cols-1 gap-4;
}

.template-card {
  @apply border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-gray-300 transition-colors;
}

.template-card.selected {
  @apply border-primary-500 ring-2 ring-primary-100;
}

.template-preview {
  @apply h-24 bg-gray-50 rounded mb-3 overflow-hidden;
}

.preview-container {
  @apply h-full;
}

.preview-section {
  @apply space-y-1;
}

.preview-field {
  @apply rounded;
}

.template-info {
  @apply space-y-1;
}

.template-name {
  @apply text-sm font-medium text-gray-900;
}

.template-description {
  @apply text-xs text-gray-500;
}

.template-meta {
  @apply flex items-center justify-between text-xs text-gray-400;
}

.template-actions {
  @apply flex items-center space-x-1 mt-3 opacity-0 transition-opacity;
}

.template-card:hover .template-actions {
  @apply opacity-100;
}

.action-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.action-btn-danger {
  @apply hover:text-red-600;
}

.template-editor-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
}

.modal-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply p-4 space-y-4;
}

.modal-footer {
  @apply flex items-center justify-end space-x-2 p-4 border-t border-gray-200;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input,
.form-textarea,
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.form-textarea {
  @apply resize-none;
}

.layout-config {
  @apply space-y-3;
}

.config-section {
  @apply space-y-2;
}

.config-title {
  @apply text-sm font-medium text-gray-700;
}

.form-range {
  @apply w-full;
}

.range-value {
  @apply text-sm text-gray-600;
}

.btn {
  @apply px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200;
}
</style>
