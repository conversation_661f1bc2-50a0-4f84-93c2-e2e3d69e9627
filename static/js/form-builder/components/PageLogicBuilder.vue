<template>
  <div class="page-logic-builder-modal">
    <div class="modal-backdrop" @click="$emit('close')"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Page Logic Builder</h3>
        <p class="modal-subtitle">Configure conditional logic for {{ page.title || 'this page' }}</p>
        <button @click="$emit('close')" class="modal-close">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <!-- Logic Rules -->
        <div class="logic-section">
          <div class="section-header">
            <h4 class="section-title">Show/Hide Logic</h4>
            <p class="section-description">Control when this page should be shown or hidden</p>
          </div>

          <div class="logic-rules">
            <div v-if="visibilityRules.length === 0" class="empty-state">
              <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              <p class="empty-text">No visibility rules defined</p>
              <p class="empty-subtext">This page will always be shown</p>
            </div>

            <div v-for="(rule, index) in visibilityRules" :key="rule.id" class="logic-rule">
              <div class="rule-header">
                <span class="rule-label">Rule {{ index + 1 }}</span>
                <div class="rule-actions">
                  <button @click="duplicateRule(rule)" class="rule-action-btn" title="Duplicate rule">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                  <button @click="deleteRule(rule.id)" class="rule-action-btn rule-action-delete" title="Delete rule">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="rule-content">
                <!-- Rule Logic Operator -->
                <div class="rule-operator" v-if="index > 0">
                  <select v-model="rule.logicOperator" class="operator-select">
                    <option value="AND">AND</option>
                    <option value="OR">OR</option>
                  </select>
                </div>

                <!-- Rule Action -->
                <div class="rule-action">
                  <select v-model="rule.action" class="action-select">
                    <option value="show">Show this page when</option>
                    <option value="hide">Hide this page when</option>
                    <option value="skip">Skip this page when</option>
                    <option value="redirect">Redirect to page when</option>
                  </select>
                  
                  <select 
                    v-if="rule.action === 'redirect'"
                    v-model="rule.redirectPageId"
                    class="redirect-select"
                  >
                    <option value="">Select page...</option>
                    <option 
                      v-for="p in availablePages" 
                      :key="p.id" 
                      :value="p.id"
                    >
                      {{ p.title || `Page ${getPageIndex(p.id) + 1}` }}
                    </option>
                  </select>
                </div>

                <!-- Conditions -->
                <div class="rule-conditions">
                  <div v-for="(condition, condIndex) in rule.conditions" :key="condition.id" class="condition">
                    <div class="condition-operator" v-if="condIndex > 0">
                      <select v-model="condition.operator" class="condition-operator-select">
                        <option value="AND">AND</option>
                        <option value="OR">OR</option>
                      </select>
                    </div>

                    <div class="condition-content">
                      <!-- Field Selection -->
                      <select v-model="condition.fieldId" class="condition-field-select">
                        <option value="">Select field...</option>
                        <option 
                          v-for="field in availableFields" 
                          :key="field.id" 
                          :value="field.id"
                        >
                          {{ field.label || field.name }}
                        </option>
                      </select>

                      <!-- Comparison Operator -->
                      <select v-model="condition.comparison" class="condition-comparison-select">
                        <option value="equals">equals</option>
                        <option value="not_equals">does not equal</option>
                        <option value="contains">contains</option>
                        <option value="not_contains">does not contain</option>
                        <option value="starts_with">starts with</option>
                        <option value="ends_with">ends with</option>
                        <option value="is_empty">is empty</option>
                        <option value="is_not_empty">is not empty</option>
                        <option value="greater_than">is greater than</option>
                        <option value="less_than">is less than</option>
                        <option value="greater_equal">is greater than or equal to</option>
                        <option value="less_equal">is less than or equal to</option>
                        <option value="is_checked">is checked</option>
                        <option value="is_not_checked">is not checked</option>
                      </select>

                      <!-- Value Input -->
                      <div class="condition-value" v-if="!['is_empty', 'is_not_empty', 'is_checked', 'is_not_checked'].includes(condition.comparison)">
                        <input 
                          v-if="getFieldType(condition.fieldId) === 'text'"
                          v-model="condition.value"
                          type="text"
                          class="condition-value-input"
                          placeholder="Enter value..."
                        />
                        
                        <input 
                          v-else-if="['number', 'integer', 'decimal'].includes(getFieldType(condition.fieldId))"
                          v-model="condition.value"
                          type="number"
                          class="condition-value-input"
                          placeholder="Enter number..."
                        />
                        
                        <select 
                          v-else-if="['select', 'radio'].includes(getFieldType(condition.fieldId))"
                          v-model="condition.value"
                          class="condition-value-select"
                        >
                          <option value="">Select option...</option>
                          <option 
                            v-for="option in getFieldOptions(condition.fieldId)" 
                            :key="option.value" 
                            :value="option.value"
                          >
                            {{ option.label }}
                          </option>
                        </select>
                        
                        <input 
                          v-else
                          v-model="condition.value"
                          type="text"
                          class="condition-value-input"
                          placeholder="Enter value..."
                        />
                      </div>

                      <!-- Remove Condition -->
                      <button 
                        @click="removeCondition(rule.id, condition.id)"
                        class="remove-condition-btn"
                        title="Remove condition"
                      >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- Add Condition -->
                  <button @click="addCondition(rule.id)" class="add-condition-btn">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Add Condition
                  </button>
                </div>
              </div>
            </div>

            <!-- Add Rule -->
            <button @click="addRule" class="add-rule-btn">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add Logic Rule
            </button>
          </div>
        </div>

        <!-- Navigation Logic -->
        <div class="logic-section">
          <div class="section-header">
            <h4 class="section-title">Navigation Logic</h4>
            <p class="section-description">Control page navigation behavior</p>
          </div>

          <div class="navigation-settings">
            <div class="setting-group">
              <label class="setting-label">
                <input 
                  type="checkbox" 
                  v-model="navigationSettings.allowBack"
                  class="setting-checkbox"
                />
                Allow users to go back to this page
              </label>
            </div>

            <div class="setting-group">
              <label class="setting-label">
                <input 
                  type="checkbox" 
                  v-model="navigationSettings.allowSkip"
                  class="setting-checkbox"
                />
                Allow users to skip this page
              </label>
            </div>

            <div class="setting-group">
              <label class="setting-label">
                <input 
                  type="checkbox" 
                  v-model="navigationSettings.autoAdvance"
                  class="setting-checkbox"
                />
                Automatically advance to next page when complete
              </label>
            </div>

            <div class="setting-group">
              <label class="setting-label">Auto-advance delay (seconds)</label>
              <input 
                v-model="navigationSettings.autoAdvanceDelay"
                type="number"
                min="0"
                max="10"
                step="0.5"
                class="setting-input"
                :disabled="!navigationSettings.autoAdvance"
              />
            </div>
          </div>
        </div>

        <!-- Preview -->
        <div class="logic-preview">
          <h4 class="preview-title">Logic Preview</h4>
          <div class="preview-content">
            <div v-if="logicSummary" class="logic-summary">
              {{ logicSummary }}
            </div>
            <div v-else class="no-logic">
              No logic rules defined. This page will always be shown.
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">Cancel</button>
        <button @click="saveLogic" class="btn btn-primary">Save Logic</button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'PageLogicBuilder',
  props: {
    page: {
      type: Object,
      required: true
    },
    pages: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    }
  },
  emits: ['close', 'save'],
  setup(props, { emit }) {
    const visibilityRules = ref(props.page.logic?.visibilityRules || [])
    const navigationSettings = ref({
      allowBack: true,
      allowSkip: false,
      autoAdvance: false,
      autoAdvanceDelay: 2,
      ...props.page.logic?.navigationSettings
    })

    const availablePages = computed(() => {
      return props.pages.filter(p => p.id !== props.page.id)
    })

    const availableFields = computed(() => {
      return props.fields.filter(field => {
        // Only include fields from previous pages
        const fieldPageIndex = props.pages.findIndex(p => p.id === field.pageId)
        const currentPageIndex = props.pages.findIndex(p => p.id === props.page.id)
        return fieldPageIndex < currentPageIndex
      })
    })

    const logicSummary = computed(() => {
      if (visibilityRules.value.length === 0) return null
      
      const summaries = visibilityRules.value.map(rule => {
        const action = rule.action === 'show' ? 'Show' : 
                     rule.action === 'hide' ? 'Hide' : 
                     rule.action === 'skip' ? 'Skip' : 'Redirect from'
        
        const conditions = rule.conditions.map(condition => {
          const field = availableFields.value.find(f => f.id === condition.fieldId)
          const fieldName = field?.label || field?.name || 'Unknown field'
          return `${fieldName} ${condition.comparison} ${condition.value || ''}`
        }).join(` ${rule.conditions[0]?.operator || 'AND'} `)
        
        return `${action} this page when: ${conditions}`
      })
      
      return summaries.join(' OR ')
    })

    const getPageIndex = (pageId) => {
      return props.pages.findIndex(p => p.id === pageId)
    }

    const getFieldType = (fieldId) => {
      const field = props.fields.find(f => f.id === fieldId)
      return field?.field_type || 'text'
    }

    const getFieldOptions = (fieldId) => {
      const field = props.fields.find(f => f.id === fieldId)
      return field?.options || []
    }

    const addRule = () => {
      const newRule = {
        id: `rule-${Date.now()}`,
        action: 'show',
        logicOperator: 'AND',
        conditions: [{
          id: `condition-${Date.now()}`,
          fieldId: '',
          comparison: 'equals',
          value: '',
          operator: 'AND'
        }],
        redirectPageId: ''
      }
      visibilityRules.value.push(newRule)
    }

    const deleteRule = (ruleId) => {
      visibilityRules.value = visibilityRules.value.filter(rule => rule.id !== ruleId)
    }

    const duplicateRule = (rule) => {
      const duplicatedRule = {
        ...rule,
        id: `rule-${Date.now()}`,
        conditions: rule.conditions.map(condition => ({
          ...condition,
          id: `condition-${Date.now()}`
        }))
      }
      visibilityRules.value.push(duplicatedRule)
    }

    const addCondition = (ruleId) => {
      const rule = visibilityRules.value.find(r => r.id === ruleId)
      if (rule) {
        rule.conditions.push({
          id: `condition-${Date.now()}`,
          fieldId: '',
          comparison: 'equals',
          value: '',
          operator: 'AND'
        })
      }
    }

    const removeCondition = (ruleId, conditionId) => {
      const rule = visibilityRules.value.find(r => r.id === ruleId)
      if (rule) {
        rule.conditions = rule.conditions.filter(c => c.id !== conditionId)
        if (rule.conditions.length === 0) {
          deleteRule(ruleId)
        }
      }
    }

    const saveLogic = () => {
      const logic = {
        visibilityRules: visibilityRules.value,
        navigationSettings: navigationSettings.value
      }
      emit('save', props.page.id, logic)
    }

    return {
      visibilityRules,
      navigationSettings,
      availablePages,
      availableFields,
      logicSummary,
      getPageIndex,
      getFieldType,
      getFieldOptions,
      addRule,
      deleteRule,
      duplicateRule,
      addCondition,
      removeCondition,
      saveLogic
    }
  }
}
</script>

<style scoped>
.page-logic-builder-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col;
}

.modal-header {
  @apply flex items-start justify-between p-6 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply flex-1 overflow-y-auto p-6 space-y-8;
}

.modal-footer {
  @apply flex items-center justify-end space-x-2 p-6 border-t border-gray-200;
}

.logic-section {
  @apply space-y-4;
}

.section-header {
  @apply space-y-1;
}

.section-title {
  @apply text-base font-medium text-gray-900;
}

.section-description {
  @apply text-sm text-gray-600;
}

.empty-state {
  @apply text-center py-8;
}

.empty-icon {
  @apply w-12 h-12 mx-auto text-gray-400 mb-3;
}

.empty-text {
  @apply text-sm font-medium text-gray-900 mb-1;
}

.empty-subtext {
  @apply text-xs text-gray-500;
}

.logic-rule {
  @apply border border-gray-200 rounded-lg p-4 space-y-4;
}

.rule-header {
  @apply flex items-center justify-between;
}

.rule-label {
  @apply text-sm font-medium text-gray-700;
}

.rule-actions {
  @apply flex items-center space-x-1;
}

.rule-action-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.rule-action-delete {
  @apply hover:text-red-600;
}

.rule-content {
  @apply space-y-3;
}

.rule-operator {
  @apply flex items-center;
}

.operator-select {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.rule-action {
  @apply flex items-center space-x-2;
}

.action-select,
.redirect-select {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.rule-conditions {
  @apply space-y-3;
}

.condition {
  @apply space-y-2;
}

.condition-operator {
  @apply flex items-center;
}

.condition-operator-select {
  @apply px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.condition-content {
  @apply flex items-center space-x-2;
}

.condition-field-select,
.condition-comparison-select,
.condition-value-select {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.condition-value-input {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.remove-condition-btn {
  @apply p-1 text-gray-400 hover:text-red-600 rounded transition-colors;
}

.add-condition-btn,
.add-rule-btn {
  @apply flex items-center space-x-1 px-3 py-2 text-sm text-primary-600 border border-primary-300 rounded-md hover:bg-primary-50 transition-colors;
}

.navigation-settings {
  @apply space-y-4;
}

.setting-group {
  @apply space-y-2;
}

.setting-label {
  @apply flex items-center space-x-2 text-sm text-gray-700 cursor-pointer;
}

.setting-checkbox {
  @apply form-checkbox text-primary-600 focus:ring-primary-500;
}

.setting-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50 disabled:text-gray-500;
}

.logic-preview {
  @apply bg-gray-50 rounded-lg p-4;
}

.preview-title {
  @apply text-sm font-medium text-gray-900 mb-2;
}

.preview-content {
  @apply text-sm text-gray-600;
}

.logic-summary {
  @apply font-mono text-xs bg-white p-3 rounded border;
}

.no-logic {
  @apply italic;
}

.btn {
  @apply px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200;
}
</style>
