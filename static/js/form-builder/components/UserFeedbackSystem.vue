<template>
  <div class="user-feedback-system">
    <!-- Feedback Trigger <PERSON> -->
    <button 
      @click="showFeedbackModal = true"
      class="feedback-trigger"
      :class="{ 'has-notification': hasUnreadFeedback }"
      title="Send feedback"
    >
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.657-.398L5.5 21.5l1.902-4.843A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
      </svg>
      <span class="feedback-text">Feedback</span>
      <div v-if="hasUnreadFeedback" class="notification-dot"></div>
    </button>

    <!-- Quick Rating Widget -->
    <div v-if="showQuickRating" class="quick-rating-widget">
      <div class="rating-header">
        <h4 class="rating-title">How's your experience?</h4>
        <button @click="dismissQuickRating" class="dismiss-btn">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div class="rating-options">
        <button 
          v-for="rating in quickRatingOptions"
          :key="rating.value"
          @click="submitQuickRating(rating.value)"
          class="rating-option"
          :title="rating.label"
        >
          <component :is="rating.icon" class="w-6 h-6" />
        </button>
      </div>
    </div>

    <!-- Feedback Modal -->
    <div v-if="showFeedbackModal" class="feedback-modal">
      <div class="modal-backdrop" @click="closeFeedbackModal"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Send Feedback</h3>
          <button @click="closeFeedbackModal" class="close-btn">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <!-- Feedback Type Selection -->
          <div class="feedback-type-section">
            <label class="section-label">What type of feedback do you have?</label>
            <div class="feedback-types">
              <button 
                v-for="type in feedbackTypes"
                :key="type.value"
                @click="selectedFeedbackType = type.value"
                class="feedback-type-btn"
                :class="{ 'active': selectedFeedbackType === type.value }"
              >
                <component :is="type.icon" class="w-5 h-5" />
                <span>{{ type.label }}</span>
              </button>
            </div>
          </div>

          <!-- Rating Section -->
          <div class="rating-section">
            <label class="section-label">Overall Rating</label>
            <div class="star-rating">
              <button 
                v-for="star in 5"
                :key="star"
                @click="selectedRating = star"
                @mouseover="hoverRating = star"
                @mouseleave="hoverRating = 0"
                class="star-btn"
                :class="{ 
                  'filled': star <= (hoverRating || selectedRating),
                  'hover': star <= hoverRating 
                }"
              >
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </button>
            </div>
            <span class="rating-text">{{ getRatingText(selectedRating) }}</span>
          </div>

          <!-- Feedback Content -->
          <div class="feedback-content-section">
            <label class="section-label">
              Tell us more 
              <span class="optional">(optional)</span>
            </label>
            <textarea 
              v-model="feedbackContent"
              class="feedback-textarea"
              rows="4"
              :placeholder="getFeedbackPlaceholder()"
            ></textarea>
            <div class="character-count">
              {{ feedbackContent.length }}/1000
            </div>
          </div>

          <!-- Feature Specific Feedback -->
          <div v-if="currentFeature" class="feature-feedback-section">
            <label class="section-label">About {{ currentFeature.name }}</label>
            <div class="feature-questions">
              <div class="question-item">
                <label class="question-label">How easy was it to use?</label>
                <div class="ease-rating">
                  <button 
                    v-for="(label, index) in easeLabels"
                    :key="index"
                    @click="featureFeedback.ease = index + 1"
                    class="ease-btn"
                    :class="{ 'active': featureFeedback.ease === index + 1 }"
                  >
                    {{ label }}
                  </button>
                </div>
              </div>
              
              <div class="question-item">
                <label class="question-label">Did it meet your expectations?</label>
                <div class="expectation-rating">
                  <button 
                    v-for="option in expectationOptions"
                    :key="option.value"
                    @click="featureFeedback.expectation = option.value"
                    class="expectation-btn"
                    :class="{ 'active': featureFeedback.expectation === option.value }"
                  >
                    {{ option.label }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="contact-section">
            <label class="section-label">
              Contact Information 
              <span class="optional">(optional - for follow-up)</span>
            </label>
            <input 
              v-model="contactEmail"
              type="email"
              class="contact-input"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Screenshot Option -->
          <div class="screenshot-section">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="includeScreenshot"
                class="checkbox"
              />
              Include screenshot of current page
            </label>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeFeedbackModal" class="btn btn-secondary">
            Cancel
          </button>
          <button 
            @click="submitFeedback"
            :disabled="!canSubmitFeedback"
            class="btn btn-primary"
          >
            <svg v-if="isSubmitting" class="w-4 h-4 animate-spin mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ isSubmitting ? 'Sending...' : 'Send Feedback' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="showSuccessMessage" class="success-toast">
      <div class="toast-content">
        <svg class="toast-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Thank you for your feedback!</span>
      </div>
    </div>

    <!-- Feedback History (for admin) -->
    <div v-if="showFeedbackHistory" class="feedback-history">
      <div class="history-header">
        <h3 class="history-title">Recent Feedback</h3>
        <button @click="showFeedbackHistory = false" class="close-btn">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      <div class="history-list">
        <div 
          v-for="feedback in recentFeedback"
          :key="feedback.id"
          class="feedback-item"
        >
          <div class="feedback-header">
            <div class="feedback-rating">
              <div class="stars">
                <svg 
                  v-for="star in 5"
                  :key="star"
                  class="star"
                  :class="{ 'filled': star <= feedback.rating }"
                  fill="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <span class="rating-value">{{ feedback.rating }}/5</span>
            </div>
            <span class="feedback-date">{{ formatDate(feedback.timestamp) }}</span>
          </div>
          <div class="feedback-type">{{ feedback.type }}</div>
          <div class="feedback-content">{{ feedback.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'

export default {
  name: 'UserFeedbackSystem',
  props: {
    currentFeature: {
      type: Object,
      default: null
    },
    showHistory: {
      type: Boolean,
      default: false
    }
  },
  emits: ['feedback-submitted'],
  setup(props, { emit }) {
    const showFeedbackModal = ref(false)
    const showQuickRating = ref(false)
    const showSuccessMessage = ref(false)
    const showFeedbackHistory = ref(props.showHistory)
    const hasUnreadFeedback = ref(false)
    
    const selectedFeedbackType = ref('general')
    const selectedRating = ref(0)
    const hoverRating = ref(0)
    const feedbackContent = ref('')
    const contactEmail = ref('')
    const includeScreenshot = ref(false)
    const isSubmitting = ref(false)
    
    const featureFeedback = ref({
      ease: 0,
      expectation: ''
    })
    
    const recentFeedback = ref([])
    
    const feedbackTypes = [
      { value: 'bug', label: 'Bug Report', icon: 'BugIcon' },
      { value: 'feature', label: 'Feature Request', icon: 'LightBulbIcon' },
      { value: 'improvement', label: 'Improvement', icon: 'TrendingUpIcon' },
      { value: 'general', label: 'General Feedback', icon: 'ChatIcon' }
    ]
    
    const quickRatingOptions = [
      { value: 1, label: 'Poor', icon: 'FrownIcon' },
      { value: 3, label: 'Okay', icon: 'MehIcon' },
      { value: 5, label: 'Great', icon: 'SmileIcon' }
    ]
    
    const easeLabels = ['Very Hard', 'Hard', 'Okay', 'Easy', 'Very Easy']
    
    const expectationOptions = [
      { value: 'exceeded', label: 'Exceeded' },
      { value: 'met', label: 'Met' },
      { value: 'below', label: 'Below' }
    ]
    
    const canSubmitFeedback = computed(() => {
      return selectedRating.value > 0 && selectedFeedbackType.value
    })
    
    const getRatingText = (rating) => {
      const texts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
      return texts[rating] || ''
    }
    
    const getFeedbackPlaceholder = () => {
      const placeholders = {
        bug: 'Please describe the bug you encountered, including steps to reproduce it...',
        feature: 'What feature would you like to see? How would it help you?',
        improvement: 'What could be improved? How would you like it to work?',
        general: 'Share your thoughts, suggestions, or any other feedback...'
      }
      return placeholders[selectedFeedbackType.value] || placeholders.general
    }
    
    const submitQuickRating = async (rating) => {
      try {
        await submitFeedbackData({
          type: 'quick-rating',
          rating,
          timestamp: Date.now(),
          feature: props.currentFeature?.name
        })
        
        showQuickRating.value = false
        showSuccessToast()
      } catch (error) {
        console.error('Failed to submit quick rating:', error)
      }
    }
    
    const submitFeedback = async () => {
      if (!canSubmitFeedback.value) return
      
      isSubmitting.value = true
      
      try {
        const feedbackData = {
          type: selectedFeedbackType.value,
          rating: selectedRating.value,
          content: feedbackContent.value,
          email: contactEmail.value,
          feature: props.currentFeature,
          featureFeedback: featureFeedback.value,
          includeScreenshot: includeScreenshot.value,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href
        }
        
        if (includeScreenshot.value) {
          feedbackData.screenshot = await captureScreenshot()
        }
        
        await submitFeedbackData(feedbackData)
        
        closeFeedbackModal()
        showSuccessToast()
        
        emit('feedback-submitted', feedbackData)
        
      } catch (error) {
        console.error('Failed to submit feedback:', error)
        // Show error message
      } finally {
        isSubmitting.value = false
      }
    }
    
    const submitFeedbackData = async (data) => {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value
        },
        body: JSON.stringify(data)
      })
      
      if (!response.ok) {
        throw new Error(`Failed to submit feedback: ${response.statusText}`)
      }
      
      return response.json()
    }
    
    const captureScreenshot = async () => {
      try {
        const canvas = await html2canvas(document.body)
        return canvas.toDataURL('image/png')
      } catch (error) {
        console.error('Failed to capture screenshot:', error)
        return null
      }
    }
    
    const closeFeedbackModal = () => {
      showFeedbackModal.value = false
      resetForm()
    }
    
    const resetForm = () => {
      selectedFeedbackType.value = 'general'
      selectedRating.value = 0
      hoverRating.value = 0
      feedbackContent.value = ''
      contactEmail.value = ''
      includeScreenshot.value = false
      featureFeedback.value = { ease: 0, expectation: '' }
    }
    
    const dismissQuickRating = () => {
      showQuickRating.value = false
      localStorage.setItem('feedbackQuickRatingDismissed', Date.now().toString())
    }
    
    const showSuccessToast = () => {
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 3000)
    }
    
    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const loadRecentFeedback = async () => {
      try {
        const response = await fetch('/api/feedback/recent')
        if (response.ok) {
          recentFeedback.value = await response.json()
        }
      } catch (error) {
        console.error('Failed to load recent feedback:', error)
      }
    }
    
    const checkForQuickRating = () => {
      const dismissed = localStorage.getItem('feedbackQuickRatingDismissed')
      const lastShown = localStorage.getItem('feedbackQuickRatingLastShown')
      
      if (!dismissed || (Date.now() - parseInt(dismissed)) > 7 * 24 * 60 * 60 * 1000) {
        if (!lastShown || (Date.now() - parseInt(lastShown)) > 24 * 60 * 60 * 1000) {
          setTimeout(() => {
            showQuickRating.value = true
            localStorage.setItem('feedbackQuickRatingLastShown', Date.now().toString())
          }, 30000) // Show after 30 seconds
        }
      }
    }
    
    onMounted(() => {
      checkForQuickRating()
      if (props.showHistory) {
        loadRecentFeedback()
      }
    })
    
    return {
      showFeedbackModal,
      showQuickRating,
      showSuccessMessage,
      showFeedbackHistory,
      hasUnreadFeedback,
      selectedFeedbackType,
      selectedRating,
      hoverRating,
      feedbackContent,
      contactEmail,
      includeScreenshot,
      isSubmitting,
      featureFeedback,
      recentFeedback,
      feedbackTypes,
      quickRatingOptions,
      easeLabels,
      expectationOptions,
      canSubmitFeedback,
      getRatingText,
      getFeedbackPlaceholder,
      submitQuickRating,
      submitFeedback,
      closeFeedbackModal,
      dismissQuickRating,
      formatDate
    }
  }
}
</script>

<style scoped>
.feedback-trigger {
  @apply fixed bottom-6 right-6 flex items-center space-x-2 bg-primary-600 text-white px-4 py-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-200 z-40;
}

.feedback-trigger:hover {
  @apply scale-105;
}

.feedback-trigger.has-notification {
  @apply animate-pulse;
}

.notification-dot {
  @apply absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full;
}

.quick-rating-widget {
  @apply fixed bottom-24 right-6 bg-white rounded-lg shadow-xl border border-gray-200 p-4 z-40 max-w-xs;
}

.rating-header {
  @apply flex items-center justify-between mb-3;
}

.rating-title {
  @apply text-sm font-medium text-gray-900;
}

.dismiss-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.rating-options {
  @apply flex items-center justify-center space-x-3;
}

.rating-option {
  @apply p-2 rounded-full hover:bg-gray-100 transition-colors;
}

.feedback-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply flex-1 overflow-y-auto p-6 space-y-6;
}

.section-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.optional {
  @apply text-gray-500 font-normal;
}

.feedback-types {
  @apply grid grid-cols-2 gap-3;
}

.feedback-type-btn {
  @apply flex items-center space-x-2 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors;
}

.feedback-type-btn.active {
  @apply border-primary-500 bg-primary-50 text-primary-700;
}

.star-rating {
  @apply flex items-center space-x-1 mb-2;
}

.star-btn {
  @apply text-gray-300 hover:text-yellow-400 transition-colors;
}

.star-btn.filled {
  @apply text-yellow-400;
}

.star-btn.hover {
  @apply text-yellow-300;
}

.rating-text {
  @apply text-sm text-gray-600;
}

.feedback-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none;
}

.character-count {
  @apply text-xs text-gray-500 text-right mt-1;
}

.feature-questions {
  @apply space-y-4;
}

.question-item {
  @apply space-y-2;
}

.question-label {
  @apply block text-sm font-medium text-gray-700;
}

.ease-rating,
.expectation-rating {
  @apply flex flex-wrap gap-2;
}

.ease-btn,
.expectation-btn {
  @apply px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 transition-colors;
}

.ease-btn.active,
.expectation-btn.active {
  @apply border-primary-500 bg-primary-50 text-primary-700;
}

.contact-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.checkbox-label {
  @apply flex items-center space-x-2 text-sm text-gray-700 cursor-pointer;
}

.checkbox {
  @apply form-checkbox text-primary-600 focus:ring-primary-500;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200;
}

.btn {
  @apply px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.btn-primary {
  @apply text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200;
}

.success-toast {
  @apply fixed bottom-6 left-6 bg-green-600 text-white rounded-lg shadow-lg z-50;
}

.toast-content {
  @apply flex items-center space-x-2 px-4 py-3;
}

.toast-icon {
  @apply w-5 h-5;
}

.feedback-history {
  @apply fixed top-6 right-6 bg-white rounded-lg shadow-xl border border-gray-200 max-w-md w-full z-40;
}

.history-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.history-title {
  @apply text-lg font-semibold text-gray-900;
}

.history-list {
  @apply max-h-96 overflow-y-auto p-4 space-y-4;
}

.feedback-item {
  @apply border border-gray-200 rounded-lg p-3;
}

.feedback-header {
  @apply flex items-center justify-between mb-2;
}

.feedback-rating {
  @apply flex items-center space-x-2;
}

.stars {
  @apply flex space-x-1;
}

.star {
  @apply w-4 h-4 text-gray-300;
}

.star.filled {
  @apply text-yellow-400;
}

.rating-value {
  @apply text-sm text-gray-600;
}

.feedback-date {
  @apply text-xs text-gray-500;
}

.feedback-type {
  @apply text-xs text-primary-600 font-medium mb-1;
}

.feedback-content {
  @apply text-sm text-gray-700;
}
</style>
