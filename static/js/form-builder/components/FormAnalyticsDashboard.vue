<template>
  <div class="analytics-dashboard">
    <div class="dashboard-header">
      <h2 class="dashboard-title">Form Analytics</h2>
      <div class="dashboard-controls">
        <select v-model="selectedTimeRange" class="time-range-select">
          <option value="24h">Last 24 Hours</option>
          <option value="7d">Last 7 Days</option>
          <option value="30d">Last 30 Days</option>
          <option value="90d">Last 90 Days</option>
          <option value="custom">Custom Range</option>
        </select>
        
        <button @click="refreshData" class="refresh-btn" :disabled="isLoading">
          <svg class="w-4 h-4" :class="{ 'animate-spin': isLoading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh
        </button>
        
        <button @click="exportData" class="export-btn">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Export
        </button>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-header">
          <h3 class="metric-title">Total Views</h3>
          <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
        <div class="metric-value">{{ formatNumber(analytics.totalViews) }}</div>
        <div class="metric-change" :class="getChangeClass(analytics.viewsChange)">
          {{ formatChange(analytics.viewsChange) }}
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3 class="metric-title">Submissions</h3>
          <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="metric-value">{{ formatNumber(analytics.totalSubmissions) }}</div>
        <div class="metric-change" :class="getChangeClass(analytics.submissionsChange)">
          {{ formatChange(analytics.submissionsChange) }}
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3 class="metric-title">Conversion Rate</h3>
          <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
        <div class="metric-value">{{ formatPercentage(analytics.conversionRate) }}</div>
        <div class="metric-change" :class="getChangeClass(analytics.conversionChange)">
          {{ formatChange(analytics.conversionChange) }}
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <h3 class="metric-title">Avg. Completion Time</h3>
          <svg class="metric-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="metric-value">{{ formatDuration(analytics.avgCompletionTime) }}</div>
        <div class="metric-change" :class="getChangeClass(analytics.timeChange)">
          {{ formatChange(analytics.timeChange) }}
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <!-- Submissions Over Time -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">Submissions Over Time</h3>
          <div class="chart-controls">
            <button 
              v-for="period in chartPeriods" 
              :key="period.value"
              @click="selectedChartPeriod = period.value"
              class="period-btn"
              :class="{ 'active': selectedChartPeriod === period.value }"
            >
              {{ period.label }}
            </button>
          </div>
        </div>
        <div class="chart-container">
          <LineChart 
            :data="submissionsChartData" 
            :options="chartOptions"
            height="300"
          />
        </div>
      </div>

      <!-- Field Interaction Heatmap -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">Field Interaction Heatmap</h3>
        </div>
        <div class="heatmap-container">
          <div class="heatmap-legend">
            <span class="legend-label">Less</span>
            <div class="legend-scale">
              <div class="scale-item" v-for="i in 5" :key="i" :class="`intensity-${i}`"></div>
            </div>
            <span class="legend-label">More</span>
          </div>
          <div class="heatmap-grid">
            <div 
              v-for="field in fieldInteractionData" 
              :key="field.id"
              class="heatmap-cell"
              :class="getHeatmapIntensity(field.interactions)"
              :title="`${field.label}: ${field.interactions} interactions`"
            >
              <span class="field-label">{{ field.label }}</span>
              <span class="interaction-count">{{ field.interactions }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="detailed-analytics">
      <!-- Drop-off Analysis -->
      <div class="analysis-card">
        <div class="analysis-header">
          <h3 class="analysis-title">Drop-off Analysis</h3>
          <p class="analysis-subtitle">Where users abandon the form</p>
        </div>
        <div class="dropoff-list">
          <div 
            v-for="(point, index) in dropOffPoints" 
            :key="index"
            class="dropoff-item"
          >
            <div class="dropoff-info">
              <span class="dropoff-field">{{ point.fieldLabel || point.fieldId }}</span>
              <span class="dropoff-page">Page {{ point.pageIndex + 1 }}</span>
            </div>
            <div class="dropoff-stats">
              <span class="dropoff-rate">{{ formatPercentage(point.rate) }}</span>
              <div class="dropoff-bar">
                <div 
                  class="dropoff-fill" 
                  :style="{ width: `${point.rate}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Device & Browser Stats -->
      <div class="analysis-card">
        <div class="analysis-header">
          <h3 class="analysis-title">Device & Browser</h3>
          <p class="analysis-subtitle">User device and browser breakdown</p>
        </div>
        <div class="device-stats">
          <div class="stat-section">
            <h4 class="stat-title">Devices</h4>
            <div class="stat-list">
              <div 
                v-for="device in deviceStats" 
                :key="device.name"
                class="stat-item"
              >
                <span class="stat-label">{{ device.name }}</span>
                <span class="stat-value">{{ formatPercentage(device.percentage) }}</span>
              </div>
            </div>
          </div>
          
          <div class="stat-section">
            <h4 class="stat-title">Browsers</h4>
            <div class="stat-list">
              <div 
                v-for="browser in browserStats" 
                :key="browser.name"
                class="stat-item"
              >
                <span class="stat-label">{{ browser.name }}</span>
                <span class="stat-value">{{ formatPercentage(browser.percentage) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="analysis-card">
        <div class="analysis-header">
          <h3 class="analysis-title">Performance</h3>
          <p class="analysis-subtitle">Form loading and interaction performance</p>
        </div>
        <div class="performance-metrics">
          <div class="performance-item">
            <span class="performance-label">Average Load Time</span>
            <span class="performance-value">{{ formatDuration(analytics.avgLoadTime) }}</span>
          </div>
          <div class="performance-item">
            <span class="performance-label">Average Render Time</span>
            <span class="performance-value">{{ formatDuration(analytics.avgRenderTime) }}</span>
          </div>
          <div class="performance-item">
            <span class="performance-label">Average Interaction Delay</span>
            <span class="performance-value">{{ formatDuration(analytics.avgInteractionDelay) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Real-time Activity -->
    <div class="realtime-section" v-if="showRealTime">
      <div class="realtime-header">
        <h3 class="realtime-title">
          <span class="realtime-indicator"></span>
          Real-time Activity
        </h3>
        <span class="active-users">{{ activeUsers }} active users</span>
      </div>
      <div class="realtime-feed">
        <div 
          v-for="activity in recentActivity" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <component :is="getActivityIcon(activity.type)" class="w-4 h-4" />
          </div>
          <div class="activity-content">
            <span class="activity-description">{{ activity.description }}</span>
            <span class="activity-time">{{ formatTimeAgo(activity.timestamp) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useFormAnalytics } from '../composables/useFormAnalytics'
import LineChart from './charts/LineChart.vue'

export default {
  name: 'FormAnalyticsDashboard',
  components: {
    LineChart
  },
  props: {
    formId: {
      type: String,
      required: true
    },
    showRealTime: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const selectedTimeRange = ref('7d')
    const selectedChartPeriod = ref('daily')
    const isLoading = ref(false)
    
    // Use analytics composable
    const {
      analytics,
      startTracking,
      stopTracking,
      getAnalyticsReport,
      exportAnalytics
    } = useFormAnalytics(props.formId, {
      enableRealTime: props.showRealTime
    })

    const chartPeriods = [
      { value: 'hourly', label: 'Hourly' },
      { value: 'daily', label: 'Daily' },
      { value: 'weekly', label: 'Weekly' }
    ]

    // Mock data - in real implementation, this would come from API
    const mockAnalytics = ref({
      totalViews: 1247,
      viewsChange: 12.5,
      totalSubmissions: 89,
      submissionsChange: 8.3,
      conversionRate: 7.14,
      conversionChange: -2.1,
      avgCompletionTime: 180000, // 3 minutes in ms
      timeChange: -5.2,
      avgLoadTime: 1200,
      avgRenderTime: 800,
      avgInteractionDelay: 50
    })

    const submissionsChartData = computed(() => {
      // Mock chart data
      return {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
          label: 'Submissions',
          data: [12, 19, 3, 5, 2, 3, 9],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4
        }]
      }
    })

    const fieldInteractionData = ref([
      { id: 'field1', label: 'Name', interactions: 245 },
      { id: 'field2', label: 'Email', interactions: 198 },
      { id: 'field3', label: 'Phone', interactions: 156 },
      { id: 'field4', label: 'Message', interactions: 89 }
    ])

    const dropOffPoints = ref([
      { fieldId: 'phone', fieldLabel: 'Phone Number', pageIndex: 1, rate: 23.5 },
      { fieldId: 'address', fieldLabel: 'Address', pageIndex: 2, rate: 18.2 },
      { fieldId: 'payment', fieldLabel: 'Payment Info', pageIndex: 3, rate: 15.7 }
    ])

    const deviceStats = ref([
      { name: 'Desktop', percentage: 65.2 },
      { name: 'Mobile', percentage: 28.7 },
      { name: 'Tablet', percentage: 6.1 }
    ])

    const browserStats = ref([
      { name: 'Chrome', percentage: 68.4 },
      { name: 'Safari', percentage: 18.9 },
      { name: 'Firefox', percentage: 8.2 },
      { name: 'Edge', percentage: 4.5 }
    ])

    const activeUsers = ref(3)
    const recentActivity = ref([
      { id: 1, type: 'view', description: 'User viewed form', timestamp: Date.now() - 30000 },
      { id: 2, type: 'submit', description: 'Form submitted successfully', timestamp: Date.now() - 120000 },
      { id: 3, type: 'error', description: 'Validation error on email field', timestamp: Date.now() - 180000 }
    ])

    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }

    // Utility functions
    const formatNumber = (num) => {
      return new Intl.NumberFormat().format(num)
    }

    const formatPercentage = (num) => {
      return `${num.toFixed(1)}%`
    }

    const formatDuration = (ms) => {
      if (ms < 1000) return `${ms}ms`
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
      return `${(ms / 60000).toFixed(1)}m`
    }

    const formatChange = (change) => {
      const sign = change >= 0 ? '+' : ''
      return `${sign}${change.toFixed(1)}%`
    }

    const getChangeClass = (change) => {
      return change >= 0 ? 'positive' : 'negative'
    }

    const getHeatmapIntensity = (interactions) => {
      const max = Math.max(...fieldInteractionData.value.map(f => f.interactions))
      const intensity = Math.ceil((interactions / max) * 5)
      return `intensity-${intensity}`
    }

    const getActivityIcon = (type) => {
      const icons = {
        view: 'EyeIcon',
        submit: 'CheckCircleIcon',
        error: 'ExclamationTriangleIcon'
      }
      return icons[type] || 'InformationCircleIcon'
    }

    const formatTimeAgo = (timestamp) => {
      const diff = Date.now() - timestamp
      if (diff < 60000) return 'Just now'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
      return `${Math.floor(diff / 3600000)}h ago`
    }

    const refreshData = async () => {
      isLoading.value = true
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      isLoading.value = false
    }

    const exportData = () => {
      const data = exportAnalytics('csv')
      const blob = new Blob([data], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `form-analytics-${props.formId}-${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      URL.revokeObjectURL(url)
    }

    onMounted(() => {
      if (props.showRealTime) {
        startTracking()
      }
    })

    onUnmounted(() => {
      stopTracking()
    })

    return {
      selectedTimeRange,
      selectedChartPeriod,
      isLoading,
      chartPeriods,
      analytics: mockAnalytics,
      submissionsChartData,
      fieldInteractionData,
      dropOffPoints,
      deviceStats,
      browserStats,
      activeUsers,
      recentActivity,
      chartOptions,
      formatNumber,
      formatPercentage,
      formatDuration,
      formatChange,
      getChangeClass,
      getHeatmapIntensity,
      getActivityIcon,
      formatTimeAgo,
      refreshData,
      exportData
    }
  }
}
</script>

<style scoped>
.analytics-dashboard {
  @apply p-6 space-y-6;
}

.dashboard-header {
  @apply flex items-center justify-between;
}

.dashboard-title {
  @apply text-2xl font-bold text-gray-900;
}

.dashboard-controls {
  @apply flex items-center space-x-3;
}

.time-range-select {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500;
}

.refresh-btn,
.export-btn {
  @apply flex items-center space-x-1 px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50;
}

.metrics-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.metric-card {
  @apply bg-white p-6 rounded-lg shadow border;
}

.metric-header {
  @apply flex items-center justify-between mb-4;
}

.metric-title {
  @apply text-sm font-medium text-gray-600;
}

.metric-icon {
  @apply w-5 h-5 text-gray-400;
}

.metric-value {
  @apply text-3xl font-bold text-gray-900 mb-2;
}

.metric-change {
  @apply text-sm font-medium;
}

.metric-change.positive {
  @apply text-green-600;
}

.metric-change.negative {
  @apply text-red-600;
}

.charts-section {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.chart-card {
  @apply bg-white p-6 rounded-lg shadow border;
}

.chart-header {
  @apply flex items-center justify-between mb-4;
}

.chart-title {
  @apply text-lg font-semibold text-gray-900;
}

.chart-controls {
  @apply flex space-x-1;
}

.period-btn {
  @apply px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.period-btn.active {
  @apply bg-primary-100 border-primary-300 text-primary-700;
}

.chart-container {
  @apply h-64;
}

.heatmap-container {
  @apply space-y-4;
}

.heatmap-legend {
  @apply flex items-center justify-center space-x-2 text-sm text-gray-600;
}

.legend-scale {
  @apply flex space-x-1;
}

.scale-item {
  @apply w-4 h-4 rounded;
}

.intensity-1 { @apply bg-gray-100; }
.intensity-2 { @apply bg-blue-100; }
.intensity-3 { @apply bg-blue-200; }
.intensity-4 { @apply bg-blue-400; }
.intensity-5 { @apply bg-blue-600; }

.heatmap-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-2;
}

.heatmap-cell {
  @apply p-3 rounded text-center text-sm;
}

.field-label {
  @apply block font-medium;
}

.interaction-count {
  @apply block text-xs opacity-75;
}

.detailed-analytics {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
}

.analysis-card {
  @apply bg-white p-6 rounded-lg shadow border;
}

.analysis-header {
  @apply mb-4;
}

.analysis-title {
  @apply text-lg font-semibold text-gray-900;
}

.analysis-subtitle {
  @apply text-sm text-gray-600;
}

.dropoff-list {
  @apply space-y-3;
}

.dropoff-item {
  @apply flex items-center justify-between;
}

.dropoff-info {
  @apply flex flex-col;
}

.dropoff-field {
  @apply text-sm font-medium text-gray-900;
}

.dropoff-page {
  @apply text-xs text-gray-500;
}

.dropoff-stats {
  @apply flex items-center space-x-2;
}

.dropoff-rate {
  @apply text-sm font-medium text-red-600;
}

.dropoff-bar {
  @apply w-16 h-2 bg-gray-200 rounded-full overflow-hidden;
}

.dropoff-fill {
  @apply h-full bg-red-500;
}

.device-stats {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.stat-section {
  @apply space-y-3;
}

.stat-title {
  @apply text-sm font-medium text-gray-900;
}

.stat-list {
  @apply space-y-2;
}

.stat-item {
  @apply flex items-center justify-between text-sm;
}

.stat-label {
  @apply text-gray-600;
}

.stat-value {
  @apply font-medium text-gray-900;
}

.performance-metrics {
  @apply space-y-3;
}

.performance-item {
  @apply flex items-center justify-between;
}

.performance-label {
  @apply text-sm text-gray-600;
}

.performance-value {
  @apply text-sm font-medium text-gray-900;
}

.realtime-section {
  @apply bg-white p-6 rounded-lg shadow border;
}

.realtime-header {
  @apply flex items-center justify-between mb-4;
}

.realtime-title {
  @apply flex items-center space-x-2 text-lg font-semibold text-gray-900;
}

.realtime-indicator {
  @apply w-2 h-2 bg-green-500 rounded-full animate-pulse;
}

.active-users {
  @apply text-sm text-gray-600;
}

.realtime-feed {
  @apply space-y-3 max-h-64 overflow-y-auto;
}

.activity-item {
  @apply flex items-start space-x-3;
}

.activity-icon {
  @apply flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center;
}

.activity-content {
  @apply flex-1 min-w-0;
}

.activity-description {
  @apply block text-sm text-gray-900;
}

.activity-time {
  @apply block text-xs text-gray-500;
}
</style>
