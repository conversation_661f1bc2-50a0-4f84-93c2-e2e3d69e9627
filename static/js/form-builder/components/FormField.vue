<template>
  <div 
    class="form-field-wrapper"
    :class="{ 
      'selected': isSelected,
      'field-hidden': !field.is_visible 
    }"
    @click="selectField"
  >
    <!-- Field Header -->
    <div class="field-header">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <div class="drag-handle cursor-move">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
            </svg>
          </div>
          
          <span class="field-type-badge">
            {{ getFieldTypeLabel(field.field_type) }}
          </span>
          
          <span v-if="field.required" class="text-red-500 text-xs">Required</span>
          <span v-if="!field.is_visible" class="text-gray-500 text-xs">Hidden</span>
        </div>
        
        <div class="field-actions">
          <button 
            @click.stop="duplicateField"
            class="field-action-btn"
            title="Duplicate field"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
          </button>
          
          <button 
            @click.stop="deleteField"
            class="field-action-btn text-red-600 hover:text-red-700"
            title="Delete field"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Field Preview -->
    <div class="field-preview">
      <component 
        :is="getFieldComponent(field.field_type)"
        :field="field"
        :preview="true"
        @field-updated="updateField"
      />
    </div>
    
    <!-- Field Info -->
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import TextFieldPreview from './field-previews/TextFieldPreview.vue'
import TextAreaFieldPreview from './field-previews/TextAreaFieldPreview.vue'
import EmailFieldPreview from './field-previews/EmailFieldPreview.vue'
import NumberFieldPreview from './field-previews/NumberFieldPreview.vue'
import DateFieldPreview from './field-previews/DateFieldPreview.vue'
import SelectFieldPreview from './field-previews/SelectFieldPreview.vue'
import RadioFieldPreview from './field-previews/RadioFieldPreview.vue'
import CheckboxFieldPreview from './field-previews/CheckboxFieldPreview.vue'
import FileFieldPreview from './field-previews/FileFieldPreview.vue'
import SectionFieldPreview from './field-previews/SectionFieldPreview.vue'
import HtmlFieldPreview from './field-previews/HtmlFieldPreview.vue'
import RichTextFieldPreview from './field-previews/RichTextFieldPreview.vue'
import SignatureFieldPreview from './field-previews/SignatureFieldPreview.vue'
import ColorFieldPreview from './field-previews/ColorFieldPreview.vue'
import PhoneFieldPreview from './field-previews/PhoneFieldPreview.vue'
import AddressFieldPreview from './field-previews/AddressFieldPreview.vue'
import MatrixFieldPreview from './field-previews/MatrixFieldPreview.vue'

export default {
  name: 'FormField',
  components: {
    TextFieldPreview,
    TextAreaFieldPreview,
    EmailFieldPreview,
    NumberFieldPreview,
    DateFieldPreview,
    SelectFieldPreview,
    RadioFieldPreview,
    CheckboxFieldPreview,
    FileFieldPreview,
    SectionFieldPreview,
    HtmlFieldPreview,
    RichTextFieldPreview,
    SignatureFieldPreview,
    ColorFieldPreview,
    PhoneFieldPreview,
    AddressFieldPreview,
    MatrixFieldPreview
  },
  props: {
    field: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['field-selected', 'field-updated', 'field-deleted', 'field-duplicated'],
  setup(props, { emit }) {
    const selectField = () => {
      emit('field-selected', props.field)
    }
    
    const updateField = (fieldData) => {
      emit('field-updated', { ...props.field, ...fieldData })
    }
    
    const deleteField = () => {
      emit('field-deleted', props.field.id)
    }
    
    const duplicateField = () => {
      emit('field-duplicated', props.field)
    }
    
    const getFieldTypeLabel = (fieldType) => {
      const labels = {
        text: 'Text',
        textarea: 'Text Area',
        email: 'Email',
        url: 'URL',
        number: 'Number',
        integer: 'Integer',
        decimal: 'Decimal',
        date: 'Date',
        time: 'Time',
        datetime: 'Date & Time',
        select: 'Dropdown',
        radio: 'Radio',
        checkbox: 'Checkbox',
        checkbox_single: 'Single Checkbox',
        file: 'File',
        image: 'Image',
        rating: 'Rating',
        slider: 'Slider',
        signature: 'Signature',
        rich_text: 'Rich Text',
        color: 'Color',
        phone: 'Phone',
        address: 'Address',
        matrix: 'Matrix',
        section: 'Section',
        html: 'HTML',
        hidden: 'Hidden'
      }

      return labels[fieldType] || fieldType
    }
    
    const getFieldComponent = (fieldType) => {
      const components = {
        text: 'TextFieldPreview',
        textarea: 'TextAreaFieldPreview',
        email: 'EmailFieldPreview',
        url: 'TextFieldPreview',
        password: 'TextFieldPreview',
        number: 'NumberFieldPreview',
        integer: 'NumberFieldPreview',
        decimal: 'NumberFieldPreview',
        date: 'DateFieldPreview',
        time: 'DateFieldPreview',
        datetime: 'DateFieldPreview',
        select: 'SelectFieldPreview',
        radio: 'RadioFieldPreview',
        checkbox: 'CheckboxFieldPreview',
        checkbox_single: 'CheckboxFieldPreview',
        file: 'FileFieldPreview',
        image: 'FileFieldPreview',
        rating: 'NumberFieldPreview',
        slider: 'NumberFieldPreview',
        signature: 'SignatureFieldPreview',
        rich_text: 'RichTextFieldPreview',
        color: 'ColorFieldPreview',
        phone: 'PhoneFieldPreview',
        address: 'AddressFieldPreview',
        matrix: 'MatrixFieldPreview',
        section: 'SectionFieldPreview',
        html: 'HtmlFieldPreview',
        hidden: 'TextFieldPreview'
      }

      return components[fieldType] || 'TextFieldPreview'
    }
    
    return {
      selectField,
      updateField,
      deleteField,
      duplicateField,
      getFieldTypeLabel,
      getFieldComponent
    }
  }
}
</script>

<style scoped>
.form-field-wrapper {
  @apply relative p-4 bg-white border border-gray-200 rounded-lg cursor-pointer transition-all duration-200;
}

.form-field-wrapper:hover {
  @apply border-gray-300 shadow-sm;
}

.form-field-wrapper.selected {
  @apply border-primary-500 ring-2 ring-primary-100;
}

.form-field-wrapper.field-hidden {
  @apply opacity-60;
}

.field-header {
  @apply mb-3;
}

.field-type-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full;
}

.field-actions {
  @apply flex items-center space-x-1 opacity-0 transition-opacity;
}

.form-field-wrapper:hover .field-actions {
  @apply opacity-100;
}

.field-action-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.field-preview {
  @apply pointer-events-none;
}

.field-help {
  @apply mt-2 text-sm text-gray-500;
}

.drag-handle {
  @apply opacity-0 transition-opacity;
}

.form-field-wrapper:hover .drag-handle {
  @apply opacity-100;
}
</style>
