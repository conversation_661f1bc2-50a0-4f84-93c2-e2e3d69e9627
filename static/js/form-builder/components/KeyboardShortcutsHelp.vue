<template>
  <div class="keyboard-shortcuts-help">
    <!-- Overlay -->
    <div class="help-overlay" @click="$emit('close')"></div>
    
    <!-- Help Modal -->
    <div class="help-modal">
      <div class="help-header">
        <h2 class="help-title">
          <svg class="help-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
          </svg>
          Keyboard Shortcuts
        </h2>
        <button @click="$emit('close')" class="close-btn">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="help-content">
        <!-- Search -->
        <div class="help-search">
          <div class="search-input-wrapper">
            <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            <input 
              v-model="searchQuery"
              type="text"
              placeholder="Search shortcuts..."
              class="search-input"
            />
          </div>
        </div>

        <!-- Category Tabs -->
        <div class="category-tabs">
          <button 
            v-for="category in categories"
            :key="category"
            @click="selectedCategory = category"
            class="category-tab"
            :class="{ 'active': selectedCategory === category }"
          >
            {{ category }}
          </button>
        </div>

        <!-- Shortcuts List -->
        <div class="shortcuts-list">
          <div v-if="filteredShortcuts.length === 0" class="no-shortcuts">
            <svg class="no-shortcuts-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="no-shortcuts-text">No shortcuts found</p>
            <p class="no-shortcuts-subtext">Try adjusting your search or category filter</p>
          </div>

          <div v-else class="shortcuts-grid">
            <div 
              v-for="shortcut in filteredShortcuts" 
              :key="shortcut.combo"
              class="shortcut-item"
              :class="{ 'recently-used': isRecentlyUsed(shortcut.combo) }"
            >
              <div class="shortcut-keys">
                <kbd 
                  v-for="key in parseKeyCombo(shortcut.combo)" 
                  :key="key"
                  class="key"
                >
                  {{ formatKey(key) }}
                </kbd>
              </div>
              <div class="shortcut-info">
                <span class="shortcut-description">{{ shortcut.description }}</span>
                <span class="shortcut-category">{{ shortcut.category }}</span>
              </div>
              <div class="shortcut-actions">
                <button 
                  @click="testShortcut(shortcut)"
                  class="test-btn"
                  title="Test this shortcut"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2 4H7a2 2 0 01-2-2V8a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Recently Used -->
        <div v-if="recentlyUsedShortcuts.length > 0" class="recently-used-section">
          <h3 class="section-title">Recently Used</h3>
          <div class="recent-shortcuts">
            <div 
              v-for="shortcut in recentlyUsedShortcuts.slice(0, 5)" 
              :key="shortcut.combo"
              class="recent-shortcut"
            >
              <div class="recent-keys">
                <kbd 
                  v-for="key in parseKeyCombo(shortcut.combo)" 
                  :key="key"
                  class="key small"
                >
                  {{ formatKey(key) }}
                </kbd>
              </div>
              <span class="recent-description">{{ shortcut.description }}</span>
            </div>
          </div>
        </div>

        <!-- Tips -->
        <div class="tips-section">
          <h3 class="section-title">Pro Tips</h3>
          <div class="tips-list">
            <div class="tip-item">
              <svg class="tip-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
              <div class="tip-content">
                <span class="tip-title">Hold Shift</span>
                <span class="tip-text">Hold Shift while using navigation shortcuts to select multiple fields</span>
              </div>
            </div>
            
            <div class="tip-item">
              <svg class="tip-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              <div class="tip-content">
                <span class="tip-title">Quick Actions</span>
                <span class="tip-text">Use Ctrl+1-5 to quickly add common field types</span>
              </div>
            </div>
            
            <div class="tip-item">
              <svg class="tip-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <div class="tip-content">
                <span class="tip-title">Preview Mode</span>
                <span class="tip-text">Press Ctrl+P for quick preview, Ctrl+Shift+P for fullscreen</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="help-footer">
        <div class="footer-info">
          <span class="shortcut-count">{{ totalShortcuts }} shortcuts available</span>
          <span class="platform-info">{{ platformInfo }}</span>
        </div>
        <div class="footer-actions">
          <button @click="printShortcuts" class="footer-btn">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            Print
          </button>
          <button @click="exportShortcuts" class="footer-btn">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'

export default {
  name: 'KeyboardShortcutsHelp',
  props: {
    shortcuts: {
      type: Object,
      required: true
    },
    recentlyUsed: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'test-shortcut'],
  setup(props, { emit }) {
    const searchQuery = ref('')
    const selectedCategory = ref('All')
    
    const categories = computed(() => {
      const cats = new Set(['All'])
      Object.values(props.shortcuts).forEach(shortcut => {
        if (shortcut.category) {
          cats.add(shortcut.category)
        }
      })
      return Array.from(cats)
    })
    
    const shortcutsList = computed(() => {
      return Object.entries(props.shortcuts).map(([combo, config]) => ({
        combo,
        description: config.description || 'No description',
        category: config.category || 'Other',
        action: config.action || config
      }))
    })
    
    const filteredShortcuts = computed(() => {
      let filtered = shortcutsList.value
      
      // Filter by category
      if (selectedCategory.value !== 'All') {
        filtered = filtered.filter(s => s.category === selectedCategory.value)
      }
      
      // Filter by search query
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(s => 
          s.combo.toLowerCase().includes(query) ||
          s.description.toLowerCase().includes(query) ||
          s.category.toLowerCase().includes(query)
        )
      }
      
      return filtered.sort((a, b) => a.description.localeCompare(b.description))
    })
    
    const recentlyUsedShortcuts = computed(() => {
      return props.recentlyUsed.map(combo => {
        const shortcut = props.shortcuts[combo]
        return {
          combo,
          description: shortcut?.description || 'Unknown',
          category: shortcut?.category || 'Other'
        }
      })
    })
    
    const totalShortcuts = computed(() => shortcutsList.value.length)
    
    const platformInfo = computed(() => {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0
      return isMac ? 'macOS shortcuts shown' : 'Windows/Linux shortcuts shown'
    })
    
    const parseKeyCombo = (combo) => {
      return combo.split('+').map(key => key.trim())
    }
    
    const formatKey = (key) => {
      const keyMap = {
        'ctrl': '⌃',
        'cmd': '⌘',
        'shift': '⇧',
        'alt': '⌥',
        'option': '⌥',
        'meta': '⌘',
        'up': '↑',
        'down': '↓',
        'left': '←',
        'right': '→',
        'enter': '↵',
        'space': '␣',
        'tab': '⇥',
        'escape': '⎋',
        'backspace': '⌫',
        'delete': '⌦'
      }
      
      return keyMap[key.toLowerCase()] || key.toUpperCase()
    }
    
    const isRecentlyUsed = (combo) => {
      return props.recentlyUsed.includes(combo)
    }
    
    const testShortcut = (shortcut) => {
      emit('test-shortcut', shortcut)
    }
    
    const printShortcuts = () => {
      const printContent = generatePrintContent()
      const printWindow = window.open('', '_blank')
      printWindow.document.write(printContent)
      printWindow.document.close()
      printWindow.print()
    }
    
    const exportShortcuts = () => {
      const data = {
        shortcuts: shortcutsList.value,
        exportedAt: new Date().toISOString(),
        platform: navigator.platform
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'form-builder-shortcuts.json'
      a.click()
      URL.revokeObjectURL(url)
    }
    
    const generatePrintContent = () => {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Form Builder Keyboard Shortcuts</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; border-bottom: 2px solid #3B82F6; padding-bottom: 10px; }
            .category { margin: 20px 0; }
            .category h2 { color: #666; font-size: 18px; }
            .shortcut { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
            .keys { font-family: monospace; font-weight: bold; }
            kbd { background: #f0f0f0; padding: 2px 6px; border-radius: 3px; margin: 0 2px; }
          </style>
        </head>
        <body>
          <h1>Form Builder Keyboard Shortcuts</h1>
          ${categories.value.filter(cat => cat !== 'All').map(category => `
            <div class="category">
              <h2>${category}</h2>
              ${filteredShortcuts.value.filter(s => s.category === category).map(shortcut => `
                <div class="shortcut">
                  <span class="keys">${parseKeyCombo(shortcut.combo).map(key => `<kbd>${formatKey(key)}</kbd>`).join('')}</span>
                  <span class="description">${shortcut.description}</span>
                </div>
              `).join('')}
            </div>
          `).join('')}
        </body>
        </html>
      `
    }
    
    // Focus search input when modal opens
    onMounted(() => {
      setTimeout(() => {
        const searchInput = document.querySelector('.search-input')
        if (searchInput) {
          searchInput.focus()
        }
      }, 100)
    })
    
    return {
      searchQuery,
      selectedCategory,
      categories,
      filteredShortcuts,
      recentlyUsedShortcuts,
      totalShortcuts,
      platformInfo,
      parseKeyCombo,
      formatKey,
      isRecentlyUsed,
      testShortcut,
      printShortcuts,
      exportShortcuts
    }
  }
}
</script>

<style scoped>
.keyboard-shortcuts-help {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.help-overlay {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.help-modal {
  @apply relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col;
}

.help-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.help-title {
  @apply flex items-center space-x-2 text-xl font-semibold text-gray-900;
}

.help-icon {
  @apply w-6 h-6 text-primary-600;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.help-content {
  @apply flex-1 overflow-y-auto p-6 space-y-6;
}

.help-search {
  @apply mb-6;
}

.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.category-tabs {
  @apply flex flex-wrap gap-2 mb-6;
}

.category-tab {
  @apply px-3 py-1 text-sm font-medium text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors;
}

.category-tab.active {
  @apply text-primary-600 bg-primary-100;
}

.shortcuts-list {
  @apply mb-6;
}

.no-shortcuts {
  @apply text-center py-8;
}

.no-shortcuts-icon {
  @apply w-12 h-12 mx-auto text-gray-400 mb-3;
}

.no-shortcuts-text {
  @apply text-sm font-medium text-gray-900 mb-1;
}

.no-shortcuts-subtext {
  @apply text-xs text-gray-500;
}

.shortcuts-grid {
  @apply space-y-2;
}

.shortcut-item {
  @apply flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors;
}

.shortcut-item.recently-used {
  @apply bg-primary-50 border border-primary-200;
}

.shortcut-keys {
  @apply flex items-center space-x-1;
}

.key {
  @apply inline-flex items-center px-2 py-1 text-xs font-mono font-semibold text-gray-700 bg-white border border-gray-300 rounded shadow-sm;
}

.key.small {
  @apply px-1 py-0.5 text-xs;
}

.shortcut-info {
  @apply flex-1 mx-4;
}

.shortcut-description {
  @apply block text-sm font-medium text-gray-900;
}

.shortcut-category {
  @apply block text-xs text-gray-500;
}

.shortcut-actions {
  @apply flex items-center space-x-1;
}

.test-btn {
  @apply p-1 text-gray-400 hover:text-primary-600 rounded transition-colors;
}

.recently-used-section,
.tips-section {
  @apply border-t border-gray-200 pt-6;
}

.section-title {
  @apply text-sm font-semibold text-gray-900 mb-3;
}

.recent-shortcuts {
  @apply space-y-2;
}

.recent-shortcut {
  @apply flex items-center space-x-3 p-2 bg-gray-50 rounded;
}

.recent-keys {
  @apply flex items-center space-x-1;
}

.recent-description {
  @apply text-sm text-gray-700;
}

.tips-list {
  @apply space-y-3;
}

.tip-item {
  @apply flex items-start space-x-3;
}

.tip-icon {
  @apply w-5 h-5 text-primary-600 mt-0.5;
}

.tip-content {
  @apply flex-1;
}

.tip-title {
  @apply block text-sm font-medium text-gray-900;
}

.tip-text {
  @apply block text-sm text-gray-600;
}

.help-footer {
  @apply flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50;
}

.footer-info {
  @apply flex items-center space-x-4 text-sm text-gray-600;
}

.footer-actions {
  @apply flex items-center space-x-2;
}

.footer-btn {
  @apply flex items-center space-x-1 px-3 py-2 text-sm text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}
</style>
