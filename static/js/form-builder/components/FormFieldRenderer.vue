<template>
  <div 
    class="form-field-renderer"
    :class="[
      `field-type-${field.field_type}`,
      `device-${device}`,
      { 'interactive': mode === 'interactive' },
      { 'hidden': !isVisible }
    ]"
  >
    <!-- Text Input -->
    <div v-if="field.field_type === 'text'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <input 
        :type="getInputType(field.field_type)"
        :value="fieldValue"
        @input="updateValue($event.target.value)"
        :placeholder="field.placeholder"
        :required="field.required"
        :disabled="mode === 'static'"
        class="field-input"
      />
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Email Input -->
    <div v-else-if="field.field_type === 'email'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <input 
        type="email"
        :value="fieldValue"
        @input="updateValue($event.target.value)"
        :placeholder="field.placeholder"
        :required="field.required"
        :disabled="mode === 'static'"
        class="field-input"
      />
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Number Input -->
    <div v-else-if="['number', 'integer', 'decimal'].includes(field.field_type)" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <input 
        type="number"
        :value="fieldValue"
        @input="updateValue($event.target.value)"
        :placeholder="field.placeholder"
        :required="field.required"
        :disabled="mode === 'static'"
        :min="field.properties?.min_value"
        :max="field.properties?.max_value"
        :step="field.properties?.step || (field.field_type === 'decimal' ? '0.01' : '1')"
        class="field-input"
      />
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Textarea -->
    <div v-else-if="field.field_type === 'textarea'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <textarea 
        :value="fieldValue"
        @input="updateValue($event.target.value)"
        :placeholder="field.placeholder"
        :required="field.required"
        :disabled="mode === 'static'"
        :rows="field.properties?.rows || 4"
        class="field-textarea"
      ></textarea>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Select Dropdown -->
    <div v-else-if="field.field_type === 'select'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <select 
        :value="fieldValue"
        @change="updateValue($event.target.value)"
        :required="field.required"
        :disabled="mode === 'static'"
        class="field-select"
      >
        <option value="">{{ field.placeholder || 'Select an option...' }}</option>
        <option 
          v-for="option in field.options" 
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </option>
      </select>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Radio Buttons -->
    <div v-else-if="field.field_type === 'radio'" class="field-wrapper">
      <fieldset class="field-fieldset">
        <legend v-if="field.label" class="field-legend">
          {{ field.label }}
          <span v-if="field.required" class="required-indicator">*</span>
        </legend>
        <div class="radio-group">
          <label 
            v-for="option in field.options" 
            :key="option.value"
            class="radio-label"
          >
            <input 
              type="radio"
              :name="`field_${field.id}`"
              :value="option.value"
              :checked="fieldValue === option.value"
              @change="updateValue(option.value)"
              :disabled="mode === 'static'"
              class="radio-input"
            />
            <span class="radio-text">{{ option.label }}</span>
          </label>
        </div>
      </fieldset>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Checkboxes -->
    <div v-else-if="field.field_type === 'checkbox'" class="field-wrapper">
      <fieldset class="field-fieldset">
        <legend v-if="field.label" class="field-legend">
          {{ field.label }}
          <span v-if="field.required" class="required-indicator">*</span>
        </legend>
        <div class="checkbox-group">
          <label 
            v-for="option in field.options" 
            :key="option.value"
            class="checkbox-label"
          >
            <input 
              type="checkbox"
              :value="option.value"
              :checked="Array.isArray(fieldValue) && fieldValue.includes(option.value)"
              @change="updateCheckboxValue(option.value, $event.target.checked)"
              :disabled="mode === 'static'"
              class="checkbox-input"
            />
            <span class="checkbox-text">{{ option.label }}</span>
          </label>
        </div>
      </fieldset>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Date/Time Inputs -->
    <div v-else-if="['date', 'time', 'datetime'].includes(field.field_type)" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <input 
        :type="getDateTimeType(field.field_type)"
        :value="fieldValue"
        @input="updateValue($event.target.value)"
        :required="field.required"
        :disabled="mode === 'static'"
        class="field-input"
      />
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- File Upload -->
    <div v-else-if="['file', 'image'].includes(field.field_type)" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <div class="file-upload-area">
        <input 
          type="file"
          @change="handleFileUpload"
          :accept="field.field_type === 'image' ? 'image/*' : field.properties?.accepted_types"
          :multiple="field.properties?.multiple"
          :required="field.required"
          :disabled="mode === 'static'"
          class="file-input"
          :id="`file_${field.id}`"
        />
        <label :for="`file_${field.id}`" class="file-upload-label">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          <span>{{ field.placeholder || 'Choose file(s)' }}</span>
        </label>
      </div>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Rich Text Editor -->
    <div v-else-if="field.field_type === 'rich_text'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <div class="rich-text-editor">
        <div class="editor-toolbar">
          <button type="button" class="toolbar-btn" title="Bold">B</button>
          <button type="button" class="toolbar-btn" title="Italic">I</button>
          <button type="button" class="toolbar-btn" title="Underline">U</button>
        </div>
        <div 
          class="editor-content"
          contenteditable="true"
          @input="updateValue($event.target.innerHTML)"
          :data-placeholder="field.placeholder"
        ></div>
      </div>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Signature Field -->
    <div v-else-if="field.field_type === 'signature'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <div class="signature-pad">
        <canvas 
          ref="signatureCanvas"
          class="signature-canvas"
          :width="field.properties?.width || 400"
          :height="field.properties?.height || 200"
        ></canvas>
        <button type="button" @click="clearSignature" class="signature-clear">Clear</button>
      </div>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Color Picker -->
    <div v-else-if="field.field_type === 'color'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <div class="color-picker-wrapper">
        <input 
          type="color"
          :value="fieldValue || field.properties?.default_color || '#000000'"
          @input="updateValue($event.target.value)"
          :disabled="mode === 'static'"
          class="color-picker"
        />
        <input 
          type="text"
          :value="fieldValue || field.properties?.default_color || '#000000'"
          @input="updateValue($event.target.value)"
          :placeholder="field.placeholder"
          :disabled="mode === 'static'"
          class="color-text"
        />
      </div>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Phone Number -->
    <div v-else-if="field.field_type === 'phone'" class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <div class="phone-input-wrapper">
        <select class="country-select" :disabled="mode === 'static'">
          <option value="+1">🇺🇸 +1</option>
          <option value="+44">🇬🇧 +44</option>
          <option value="+33">🇫🇷 +33</option>
        </select>
        <input 
          type="tel"
          :value="fieldValue"
          @input="updateValue($event.target.value)"
          :placeholder="field.placeholder || '(*************'"
          :required="field.required"
          :disabled="mode === 'static'"
          class="phone-input"
        />
      </div>
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>

    <!-- Section Header -->
    <div v-else-if="field.field_type === 'section'" class="section-header">
      <component 
        :is="`h${field.properties?.level || 2}`"
        class="section-title"
      >
        {{ field.label }}
      </component>
      <p v-if="field.help_text" class="section-description">{{ field.help_text }}</p>
    </div>

    <!-- HTML Content -->
    <div v-else-if="field.field_type === 'html'" class="html-content">
      <div v-html="field.properties?.content || field.label"></div>
    </div>

    <!-- Fallback for unknown field types -->
    <div v-else class="field-wrapper">
      <label v-if="field.label" class="field-label">
        {{ field.label }}
        <span v-if="field.required" class="required-indicator">*</span>
      </label>
      <input 
        type="text"
        :value="fieldValue"
        @input="updateValue($event.target.value)"
        :placeholder="field.placeholder"
        :required="field.required"
        :disabled="mode === 'static'"
        class="field-input"
      />
      <p v-if="field.help_text" class="field-help">{{ field.help_text }}</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'

export default {
  name: 'FormFieldRenderer',
  props: {
    field: {
      type: Object,
      required: true
    },
    mode: {
      type: String,
      default: 'interactive', // 'static' or 'interactive'
      validator: value => ['static', 'interactive'].includes(value)
    },
    device: {
      type: String,
      default: 'desktop',
      validator: value => ['desktop', 'tablet', 'mobile'].includes(value)
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['field-changed'],
  setup(props, { emit }) {
    const signatureCanvas = ref(null)
    
    const fieldValue = computed(() => {
      return props.formData[props.field.id] || props.field.default_value || ''
    })
    
    const isVisible = computed(() => {
      if (!props.field.is_visible) return false
      
      // Check conditional logic
      if (props.field.conditional_logic && Object.keys(props.field.conditional_logic).length > 0) {
        return evaluateConditionalLogic(props.field.conditional_logic)
      }
      
      return true
    })
    
    const updateValue = (value) => {
      if (props.mode === 'static') return
      emit('field-changed', props.field.id, value)
    }
    
    const updateCheckboxValue = (optionValue, checked) => {
      if (props.mode === 'static') return
      
      let currentValue = Array.isArray(fieldValue.value) ? [...fieldValue.value] : []
      
      if (checked) {
        if (!currentValue.includes(optionValue)) {
          currentValue.push(optionValue)
        }
      } else {
        currentValue = currentValue.filter(v => v !== optionValue)
      }
      
      emit('field-changed', props.field.id, currentValue)
    }
    
    const handleFileUpload = (event) => {
      if (props.mode === 'static') return
      
      const files = Array.from(event.target.files)
      emit('field-changed', props.field.id, files)
    }
    
    const clearSignature = () => {
      if (props.mode === 'static' || !signatureCanvas.value) return
      
      const canvas = signatureCanvas.value
      const ctx = canvas.getContext('2d')
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      emit('field-changed', props.field.id, '')
    }
    
    const getInputType = (fieldType) => {
      const typeMap = {
        text: 'text',
        email: 'email',
        url: 'url',
        password: 'password',
        number: 'number',
        integer: 'number',
        decimal: 'number'
      }
      return typeMap[fieldType] || 'text'
    }
    
    const getDateTimeType = (fieldType) => {
      const typeMap = {
        date: 'date',
        time: 'time',
        datetime: 'datetime-local'
      }
      return typeMap[fieldType] || 'date'
    }
    
    const evaluateConditionalLogic = (logic) => {
      // Simplified conditional logic evaluation
      if (!logic.conditions || logic.conditions.length === 0) return true
      
      const results = logic.conditions.map(condition => {
        const fieldValue = props.formData[condition.field_id]
        return evaluateCondition(fieldValue, condition.operator, condition.value)
      })
      
      return logic.operator === 'AND' 
        ? results.every(r => r)
        : results.some(r => r)
    }
    
    const evaluateCondition = (fieldValue, operator, conditionValue) => {
      switch (operator) {
        case 'equals':
          return fieldValue === conditionValue
        case 'not_equals':
          return fieldValue !== conditionValue
        case 'contains':
          return String(fieldValue || '').includes(conditionValue)
        case 'not_contains':
          return !String(fieldValue || '').includes(conditionValue)
        case 'is_empty':
          return !fieldValue || fieldValue === ''
        case 'is_not_empty':
          return fieldValue && fieldValue !== ''
        case 'greater_than':
          return Number(fieldValue) > Number(conditionValue)
        case 'less_than':
          return Number(fieldValue) < Number(conditionValue)
        default:
          return true
      }
    }
    
    return {
      signatureCanvas,
      fieldValue,
      isVisible,
      updateValue,
      updateCheckboxValue,
      handleFileUpload,
      clearSignature,
      getInputType,
      getDateTimeType
    }
  }
}
</script>

<style scoped>
.form-field-renderer {
  @apply transition-all duration-300;
}

.form-field-renderer.hidden {
  @apply opacity-0 h-0 overflow-hidden;
}

.field-wrapper {
  @apply space-y-2;
}

.field-label,
.field-legend {
  @apply block text-sm font-medium text-gray-700;
}

.required-indicator {
  @apply text-red-500 ml-1;
}

.field-input,
.field-textarea,
.field-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-50 disabled:text-gray-500;
}

.field-textarea {
  @apply resize-vertical;
}

.field-help {
  @apply text-sm text-gray-500;
}

.field-fieldset {
  @apply border-0 p-0 m-0;
}

.radio-group,
.checkbox-group {
  @apply space-y-2;
}

.radio-label,
.checkbox-label {
  @apply flex items-center space-x-2 text-sm text-gray-700 cursor-pointer;
}

.radio-input,
.checkbox-input {
  @apply form-radio text-primary-600 focus:ring-primary-500 disabled:opacity-50;
}

.checkbox-input {
  @apply form-checkbox;
}

.file-upload-area {
  @apply relative;
}

.file-input {
  @apply sr-only;
}

.file-upload-label {
  @apply flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors;
}

.rich-text-editor {
  @apply border border-gray-300 rounded-md overflow-hidden;
}

.editor-toolbar {
  @apply flex items-center space-x-1 p-2 bg-gray-50 border-b border-gray-200;
}

.toolbar-btn {
  @apply px-2 py-1 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors;
}

.editor-content {
  @apply p-3 min-h-[100px] focus:outline-none;
}

.editor-content:empty:before {
  content: attr(data-placeholder);
  @apply text-gray-400;
}

.signature-pad {
  @apply relative border border-gray-300 rounded-md overflow-hidden;
}

.signature-canvas {
  @apply block bg-white;
}

.signature-clear {
  @apply absolute top-2 right-2 px-2 py-1 text-xs text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.color-picker-wrapper {
  @apply flex items-center space-x-2;
}

.color-picker {
  @apply w-12 h-10 border border-gray-300 rounded cursor-pointer;
}

.color-text {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.phone-input-wrapper {
  @apply flex;
}

.country-select {
  @apply px-3 py-2 border border-gray-300 border-r-0 rounded-l-md bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.phone-input {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.section-header {
  @apply py-4;
}

.section-title {
  @apply font-semibold text-gray-900 mb-2;
}

.section-description {
  @apply text-gray-600;
}

.html-content {
  @apply py-2;
}

/* Device-specific styles */
.device-mobile .field-input,
.device-mobile .field-textarea,
.device-mobile .field-select {
  @apply text-base; /* Prevent zoom on iOS */
}

.device-mobile .radio-group,
.device-mobile .checkbox-group {
  @apply space-y-3;
}

.device-mobile .radio-label,
.device-mobile .checkbox-label {
  @apply text-base;
}
</style>
