<template>
  <div class="multi-page-form-builder">
    <!-- Page Navigation -->
    <div class="page-navigation">
      <div class="page-tabs">
        <div 
          v-for="(page, index) in pages" 
          :key="page.id"
          class="page-tab"
          :class="{ 'active': activePage === page.id }"
          @click="setActivePage(page.id)"
        >
          <div class="page-tab-content">
            <span class="page-number">{{ index + 1 }}</span>
            <span class="page-title">{{ page.title || `Page ${index + 1}` }}</span>
            <span class="field-count">({{ getPageFieldCount(page.id) }})</span>
          </div>
          
          <div class="page-tab-actions">
            <button 
              @click.stop="editPageTitle(page)"
              class="tab-action-btn"
              title="Edit page title"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </button>
            
            <button 
              v-if="pages.length > 1"
              @click.stop="deletePage(page.id)"
              class="tab-action-btn tab-action-delete"
              title="Delete page"
            >
              <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Add Page Button -->
        <button @click="addPage" class="add-page-btn">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Add Page
        </button>
      </div>
      
      <!-- Page Settings -->
      <div class="page-settings">
        <button @click="showPageSettings = !showPageSettings" class="settings-toggle">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Page Settings
        </button>
      </div>
    </div>
    
    <!-- Page Settings Panel -->
    <div v-if="showPageSettings" class="page-settings-panel">
      <div class="settings-content">
        <h4 class="settings-title">Page Settings</h4>
        
        <div class="setting-group">
          <label class="setting-label">Page Title</label>
          <input 
            v-model="currentPage.title"
            @input="updatePageSettings"
            type="text" 
            class="setting-input"
            placeholder="Enter page title"
          />
        </div>
        
        <div class="setting-group">
          <label class="setting-label">Page Description</label>
          <textarea 
            v-model="currentPage.description"
            @input="updatePageSettings"
            class="setting-textarea"
            rows="3"
            placeholder="Optional page description"
          ></textarea>
        </div>
        
        <div class="setting-group">
          <label class="setting-label">Navigation</label>
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="currentPage.showPrevious"
                @change="updatePageSettings"
                class="checkbox-input"
              />
              Show Previous Button
            </label>
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                v-model="currentPage.showNext"
                @change="updatePageSettings"
                class="checkbox-input"
              />
              Show Next Button
            </label>
          </div>
        </div>
        
        <div class="setting-group">
          <label class="setting-label">Page Logic</label>
          <select 
            v-model="currentPage.logicType"
            @change="updatePageSettings"
            class="setting-select"
          >
            <option value="always">Always Show</option>
            <option value="conditional">Show Conditionally</option>
            <option value="skip">Skip Based on Conditions</option>
          </select>
        </div>
        
        <!-- Conditional Logic Builder -->
        <div v-if="currentPage.logicType !== 'always'" class="conditional-logic">
          <ConditionalLogicBuilder 
            :conditions="currentPage.conditions"
            :available-fields="getAllFields()"
            @conditions-updated="updatePageConditions"
          />
        </div>
      </div>
    </div>
    
    <!-- Progress Indicator -->
    <div class="progress-indicator">
      <div class="progress-bar">
        <div 
          class="progress-fill"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
      <span class="progress-text">
        Page {{ currentPageIndex + 1 }} of {{ pages.length }}
      </span>
    </div>
    
    <!-- Current Page Content -->
    <div class="page-content">
      <div v-if="currentPage.title || currentPage.description" class="page-header">
        <h2 v-if="currentPage.title" class="page-title">{{ currentPage.title }}</h2>
        <p v-if="currentPage.description" class="page-description">{{ currentPage.description }}</p>
      </div>
      
      <!-- Form Canvas for Current Page -->
      <FormCanvas
        :fields="getPageFields(activePage)"
        :selected-field="selectedField"
        @field-selected="$emit('field-selected', $event)"
        @field-updated="$emit('field-updated', $event)"
        @field-deleted="$emit('field-deleted', $event)"
        @field-duplicated="$emit('field-duplicated', $event)"
        @field-added="handleFieldAdded"
        @fields-reordered="$emit('fields-reordered', $event)"
      />
    </div>
    
    <!-- Page Navigation Controls -->
    <div class="page-navigation-controls">
      <button 
        v-if="currentPageIndex > 0 && currentPage.showPrevious"
        @click="previousPage"
        class="nav-btn nav-btn-previous"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Previous
      </button>
      
      <div class="nav-spacer"></div>
      
      <button 
        v-if="currentPageIndex < pages.length - 1 && currentPage.showNext"
        @click="nextPage"
        class="nav-btn nav-btn-next"
      >
        Next
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
      
      <button 
        v-if="currentPageIndex === pages.length - 1"
        @click="$emit('form-submit')"
        class="nav-btn nav-btn-submit"
      >
        Submit Form
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import FormCanvas from './FormCanvas.vue'
import ConditionalLogicBuilder from './ConditionalLogicBuilder.vue'

export default {
  name: 'MultiPageFormBuilder',
  components: {
    FormCanvas,
    ConditionalLogicBuilder
  },
  props: {
    fields: {
      type: Array,
      required: true
    },
    selectedField: {
      type: Object,
      default: null
    },
    formPages: {
      type: Array,
      default: () => []
    }
  },
  emits: [
    'field-selected',
    'field-updated', 
    'field-deleted',
    'field-duplicated',
    'field-added',
    'fields-reordered',
    'pages-updated',
    'form-submit'
  ],
  setup(props, { emit }) {
    const activePage = ref(null)
    const showPageSettings = ref(false)
    
    // Initialize pages
    const pages = ref(props.formPages.length > 0 ? props.formPages : [
      {
        id: 'page_1',
        title: 'Page 1',
        description: '',
        showPrevious: true,
        showNext: true,
        logicType: 'always',
        conditions: [],
        order: 1
      }
    ])
    
    // Set initial active page
    if (!activePage.value && pages.value.length > 0) {
      activePage.value = pages.value[0].id
    }
    
    const currentPage = computed(() => {
      return pages.value.find(p => p.id === activePage.value) || pages.value[0]
    })
    
    const currentPageIndex = computed(() => {
      return pages.value.findIndex(p => p.id === activePage.value)
    })
    
    const progressPercentage = computed(() => {
      return ((currentPageIndex.value + 1) / pages.value.length) * 100
    })
    
    const setActivePage = (pageId) => {
      activePage.value = pageId
    }
    
    const addPage = () => {
      const newPage = {
        id: `page_${Date.now()}`,
        title: `Page ${pages.value.length + 1}`,
        description: '',
        showPrevious: true,
        showNext: true,
        logicType: 'always',
        conditions: [],
        order: pages.value.length + 1
      }
      
      pages.value.push(newPage)
      activePage.value = newPage.id
      emitPagesUpdate()
    }
    
    const deletePage = (pageId) => {
      if (pages.value.length <= 1) return
      
      const pageIndex = pages.value.findIndex(p => p.id === pageId)
      if (pageIndex > -1) {
        pages.value.splice(pageIndex, 1)
        
        // Switch to previous page or first page
        if (activePage.value === pageId) {
          activePage.value = pages.value[Math.max(0, pageIndex - 1)].id
        }
        
        emitPagesUpdate()
      }
    }
    
    const editPageTitle = (page) => {
      const newTitle = prompt('Enter page title:', page.title)
      if (newTitle !== null) {
        page.title = newTitle
        emitPagesUpdate()
      }
    }
    
    const updatePageSettings = () => {
      emitPagesUpdate()
    }
    
    const updatePageConditions = (conditions) => {
      currentPage.value.conditions = conditions
      emitPagesUpdate()
    }
    
    const getPageFields = (pageId) => {
      return props.fields.filter(field => field.page_id === pageId)
    }
    
    const getPageFieldCount = (pageId) => {
      return getPageFields(pageId).length
    }
    
    const getAllFields = () => {
      return props.fields
    }
    
    const handleFieldAdded = (fieldType, position) => {
      emit('field-added', fieldType, position, activePage.value)
    }
    
    const previousPage = () => {
      if (currentPageIndex.value > 0) {
        activePage.value = pages.value[currentPageIndex.value - 1].id
      }
    }
    
    const nextPage = () => {
      if (currentPageIndex.value < pages.value.length - 1) {
        activePage.value = pages.value[currentPageIndex.value + 1].id
      }
    }
    
    const emitPagesUpdate = () => {
      emit('pages-updated', pages.value)
    }
    
    // Watch for changes in form pages prop
    watch(() => props.formPages, (newPages) => {
      if (newPages && newPages.length > 0) {
        pages.value = newPages
        if (!activePage.value || !pages.value.find(p => p.id === activePage.value)) {
          activePage.value = pages.value[0].id
        }
      }
    }, { deep: true })
    
    return {
      activePage,
      showPageSettings,
      pages,
      currentPage,
      currentPageIndex,
      progressPercentage,
      setActivePage,
      addPage,
      deletePage,
      editPageTitle,
      updatePageSettings,
      updatePageConditions,
      getPageFields,
      getPageFieldCount,
      getAllFields,
      handleFieldAdded,
      previousPage,
      nextPage
    }
  }
}
</script>

<style scoped>
.multi-page-form-builder {
  @apply h-full flex flex-col;
}

.page-navigation {
  @apply flex items-center justify-between p-4 bg-white border-b border-gray-200;
}

.page-tabs {
  @apply flex items-center space-x-2;
}

.page-tab {
  @apply relative flex items-center px-4 py-2 bg-gray-100 rounded-lg cursor-pointer hover:bg-gray-200 transition-colors;
}

.page-tab.active {
  @apply bg-primary-100 text-primary-700;
}

.page-tab-content {
  @apply flex items-center space-x-2;
}

.page-number {
  @apply w-6 h-6 flex items-center justify-center bg-white rounded-full text-xs font-medium;
}

.page-title {
  @apply text-sm font-medium;
}

.field-count {
  @apply text-xs text-gray-500;
}

.page-tab-actions {
  @apply flex items-center space-x-1 ml-2 opacity-0 transition-opacity;
}

.page-tab:hover .page-tab-actions {
  @apply opacity-100;
}

.tab-action-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.tab-action-delete {
  @apply hover:text-red-600;
}

.add-page-btn {
  @apply flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 border border-dashed border-gray-300 rounded-lg hover:border-gray-400 hover:text-gray-700 transition-colors;
}

.settings-toggle {
  @apply flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors;
}

.page-settings-panel {
  @apply bg-gray-50 border-b border-gray-200 p-4;
}

.settings-content {
  @apply max-w-2xl space-y-4;
}

.settings-title {
  @apply text-lg font-medium text-gray-900;
}

.setting-group {
  @apply space-y-2;
}

.setting-label {
  @apply block text-sm font-medium text-gray-700;
}

.setting-input,
.setting-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.setting-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none;
}

.checkbox-group {
  @apply space-y-2;
}

.checkbox-label {
  @apply flex items-center space-x-2 text-sm text-gray-700;
}

.checkbox-input {
  @apply form-checkbox text-primary-600;
}

.progress-indicator {
  @apply flex items-center justify-between px-4 py-2 bg-gray-50 border-b border-gray-200;
}

.progress-bar {
  @apply flex-1 h-2 bg-gray-200 rounded-full mr-4 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary-500 transition-all duration-300;
}

.progress-text {
  @apply text-sm text-gray-600;
}

.page-content {
  @apply flex-1 overflow-y-auto;
}

.page-header {
  @apply p-6 bg-white border-b border-gray-200;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.page-description {
  @apply text-gray-600;
}

.page-navigation-controls {
  @apply flex items-center justify-between p-4 bg-white border-t border-gray-200;
}

.nav-spacer {
  @apply flex-1;
}

.nav-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-colors;
}

.nav-btn-previous {
  @apply text-gray-700 bg-gray-100 hover:bg-gray-200;
}

.nav-btn-next {
  @apply text-primary-700 bg-primary-100 hover:bg-primary-200;
}

.nav-btn-submit {
  @apply text-white bg-primary-600 hover:bg-primary-700;
}

.conditional-logic {
  @apply mt-4 p-4 bg-white rounded-lg border border-gray-200;
}
</style>
