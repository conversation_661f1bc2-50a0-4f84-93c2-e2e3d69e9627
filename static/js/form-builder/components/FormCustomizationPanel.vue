<template>
  <div class="form-customization-panel">
    <div class="panel-header">
      <h3 class="panel-title">Form Customization</h3>
      <button @click="resetToDefaults" class="reset-btn">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Reset
      </button>
    </div>

    <div class="panel-content">
      <!-- Theme Selection -->
      <div class="customization-section">
        <h4 class="section-title">Theme</h4>
        <div class="theme-grid">
          <div 
            v-for="theme in themes" 
            :key="theme.id"
            class="theme-option"
            :class="{ 'selected': selectedTheme === theme.id }"
            @click="selectTheme(theme.id)"
          >
            <div class="theme-preview" :style="theme.previewStyle">
              <div class="theme-preview-field"></div>
              <div class="theme-preview-field"></div>
            </div>
            <span class="theme-name">{{ theme.name }}</span>
          </div>
        </div>
      </div>

      <!-- Color Customization -->
      <div class="customization-section">
        <h4 class="section-title">Colors</h4>
        <div class="color-controls">
          <div class="color-control">
            <label class="color-label">Primary Color</label>
            <div class="color-input-group">
              <input 
                type="color" 
                v-model="customization.colors.primary"
                @input="updateColors"
                class="color-picker"
              />
              <input 
                type="text" 
                v-model="customization.colors.primary"
                @input="updateColors"
                class="color-text"
              />
            </div>
          </div>
          
          <div class="color-control">
            <label class="color-label">Background</label>
            <div class="color-input-group">
              <input 
                type="color" 
                v-model="customization.colors.background"
                @input="updateColors"
                class="color-picker"
              />
              <input 
                type="text" 
                v-model="customization.colors.background"
                @input="updateColors"
                class="color-text"
              />
            </div>
          </div>
          
          <div class="color-control">
            <label class="color-label">Text Color</label>
            <div class="color-input-group">
              <input 
                type="color" 
                v-model="customization.colors.text"
                @input="updateColors"
                class="color-picker"
              />
              <input 
                type="text" 
                v-model="customization.colors.text"
                @input="updateColors"
                class="color-text"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Typography -->
      <div class="customization-section">
        <h4 class="section-title">Typography</h4>
        <div class="typography-controls">
          <div class="control-group">
            <label class="control-label">Font Family</label>
            <select v-model="customization.typography.fontFamily" @change="updateTypography" class="control-select">
              <option value="system">System Default</option>
              <option value="Inter">Inter</option>
              <option value="Roboto">Roboto</option>
              <option value="Open Sans">Open Sans</option>
              <option value="Lato">Lato</option>
              <option value="Poppins">Poppins</option>
            </select>
          </div>
          
          <div class="control-group">
            <label class="control-label">Font Size</label>
            <div class="range-control">
              <input 
                type="range" 
                v-model="customization.typography.fontSize"
                @input="updateTypography"
                min="12" 
                max="20" 
                step="1"
                class="range-slider"
              />
              <span class="range-value">{{ customization.typography.fontSize }}px</span>
            </div>
          </div>
          
          <div class="control-group">
            <label class="control-label">Line Height</label>
            <div class="range-control">
              <input 
                type="range" 
                v-model="customization.typography.lineHeight"
                @input="updateTypography"
                min="1.2" 
                max="2.0" 
                step="0.1"
                class="range-slider"
              />
              <span class="range-value">{{ customization.typography.lineHeight }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Spacing -->
      <div class="customization-section">
        <h4 class="section-title">Spacing</h4>
        <div class="spacing-controls">
          <div class="control-group">
            <label class="control-label">Field Spacing</label>
            <div class="range-control">
              <input 
                type="range" 
                v-model="customization.spacing.fieldSpacing"
                @input="updateSpacing"
                min="8" 
                max="32" 
                step="4"
                class="range-slider"
              />
              <span class="range-value">{{ customization.spacing.fieldSpacing }}px</span>
            </div>
          </div>
          
          <div class="control-group">
            <label class="control-label">Form Padding</label>
            <div class="range-control">
              <input 
                type="range" 
                v-model="customization.spacing.formPadding"
                @input="updateSpacing"
                min="16" 
                max="64" 
                step="8"
                class="range-slider"
              />
              <span class="range-value">{{ customization.spacing.formPadding }}px</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Custom CSS -->
      <div class="customization-section">
        <h4 class="section-title">Custom CSS</h4>
        <div class="css-editor">
          <textarea 
            v-model="customization.customCSS"
            @input="updateCustomCSS"
            placeholder="/* Add your custom CSS here */
.form-container {
  /* Your styles */
}"
            class="css-textarea"
            rows="8"
          ></textarea>
          <div class="css-help">
            <p class="help-text">
              Use CSS to further customize your form appearance. 
              Available classes: .form-container, .form-field, .field-label, .field-input
            </p>
          </div>
        </div>
      </div>

      <!-- Responsive Settings -->
      <div class="customization-section">
        <h4 class="section-title">Responsive Design</h4>
        <div class="responsive-controls">
          <div class="device-tabs">
            <button 
              v-for="device in devices" 
              :key="device.id"
              class="device-tab"
              :class="{ 'active': activeDevice === device.id }"
              @click="setActiveDevice(device.id)"
            >
              <component :is="device.icon" class="w-4 h-4" />
              {{ device.name }}
            </button>
          </div>
          
          <div class="device-settings">
            <div class="control-group">
              <label class="control-label">Columns</label>
              <select 
                v-model="customization.responsive[activeDevice].columns" 
                @change="updateResponsive"
                class="control-select"
              >
                <option value="1">1 Column</option>
                <option value="2">2 Columns</option>
                <option value="3">3 Columns</option>
              </select>
            </div>
            
            <div class="control-group">
              <label class="control-label">Field Width</label>
              <select 
                v-model="customization.responsive[activeDevice].fieldWidth" 
                @change="updateResponsive"
                class="control-select"
              >
                <option value="full">Full Width</option>
                <option value="auto">Auto Width</option>
                <option value="fixed">Fixed Width</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'

export default {
  name: 'FormCustomizationPanel',
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  emits: ['customization-updated'],
  setup(props, { emit }) {
    const selectedTheme = ref('default')
    const activeDevice = ref('desktop')
    
    const themes = ref([
      {
        id: 'default',
        name: 'Default',
        previewStyle: {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      },
      {
        id: 'minimal',
        name: 'Minimal',
        previewStyle: {
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
        }
      },
      {
        id: 'modern',
        name: 'Modern',
        previewStyle: {
          background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
        }
      },
      {
        id: 'dark',
        name: 'Dark',
        previewStyle: {
          background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)'
        }
      }
    ])
    
    const devices = ref([
      { id: 'desktop', name: 'Desktop', icon: 'DesktopIcon' },
      { id: 'tablet', name: 'Tablet', icon: 'TabletIcon' },
      { id: 'mobile', name: 'Mobile', icon: 'MobileIcon' }
    ])
    
    const customization = reactive({
      theme: 'default',
      colors: {
        primary: '#3B82F6',
        background: '#FFFFFF',
        text: '#1F2937'
      },
      typography: {
        fontFamily: 'system',
        fontSize: 16,
        lineHeight: 1.5
      },
      spacing: {
        fieldSpacing: 16,
        formPadding: 24
      },
      responsive: {
        desktop: { columns: 1, fieldWidth: 'full' },
        tablet: { columns: 1, fieldWidth: 'full' },
        mobile: { columns: 1, fieldWidth: 'full' }
      },
      customCSS: ''
    })
    
    const selectTheme = (themeId) => {
      selectedTheme.value = themeId
      customization.theme = themeId
      applyTheme(themeId)
    }
    
    const applyTheme = (themeId) => {
      const themeSettings = {
        default: {
          colors: { primary: '#3B82F6', background: '#FFFFFF', text: '#1F2937' }
        },
        minimal: {
          colors: { primary: '#6B7280', background: '#F9FAFB', text: '#374151' }
        },
        modern: {
          colors: { primary: '#8B5CF6', background: '#FFFFFF', text: '#111827' }
        },
        dark: {
          colors: { primary: '#60A5FA', background: '#1F2937', text: '#F9FAFB' }
        }
      }
      
      if (themeSettings[themeId]) {
        Object.assign(customization.colors, themeSettings[themeId].colors)
      }
      
      emitUpdate()
    }
    
    const updateColors = () => {
      emitUpdate()
    }
    
    const updateTypography = () => {
      emitUpdate()
    }
    
    const updateSpacing = () => {
      emitUpdate()
    }
    
    const updateResponsive = () => {
      emitUpdate()
    }
    
    const updateCustomCSS = () => {
      emitUpdate()
    }
    
    const setActiveDevice = (deviceId) => {
      activeDevice.value = deviceId
    }
    
    const resetToDefaults = () => {
      Object.assign(customization, {
        theme: 'default',
        colors: {
          primary: '#3B82F6',
          background: '#FFFFFF',
          text: '#1F2937'
        },
        typography: {
          fontFamily: 'system',
          fontSize: 16,
          lineHeight: 1.5
        },
        spacing: {
          fieldSpacing: 16,
          formPadding: 24
        },
        responsive: {
          desktop: { columns: 1, fieldWidth: 'full' },
          tablet: { columns: 1, fieldWidth: 'full' },
          mobile: { columns: 1, fieldWidth: 'full' }
        },
        customCSS: ''
      })
      selectedTheme.value = 'default'
      emitUpdate()
    }
    
    const emitUpdate = () => {
      emit('customization-updated', { ...customization })
    }
    
    // Initialize with form's existing customization
    if (props.form.customization) {
      Object.assign(customization, props.form.customization)
      selectedTheme.value = customization.theme
    }
    
    return {
      selectedTheme,
      activeDevice,
      themes,
      devices,
      customization,
      selectTheme,
      updateColors,
      updateTypography,
      updateSpacing,
      updateResponsive,
      updateCustomCSS,
      setActiveDevice,
      resetToDefaults
    }
  }
}
</script>

<style scoped>
.form-customization-panel {
  @apply h-full flex flex-col bg-white;
}

.panel-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.panel-title {
  @apply text-lg font-semibold text-gray-900;
}

.reset-btn {
  @apply flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors;
}

.panel-content {
  @apply flex-1 overflow-y-auto p-4 space-y-6;
}

.customization-section {
  @apply space-y-3;
}

.section-title {
  @apply text-sm font-medium text-gray-900;
}

.theme-grid {
  @apply grid grid-cols-2 gap-3;
}

.theme-option {
  @apply p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-gray-300 transition-colors;
}

.theme-option.selected {
  @apply border-primary-500 ring-2 ring-primary-100;
}

.theme-preview {
  @apply h-12 rounded mb-2 flex items-center justify-center space-x-1;
}

.theme-preview-field {
  @apply w-8 h-2 bg-white bg-opacity-30 rounded;
}

.theme-name {
  @apply text-xs font-medium text-gray-700;
}

.color-controls {
  @apply space-y-3;
}

.color-control {
  @apply space-y-1;
}

.color-label {
  @apply text-sm text-gray-700;
}

.color-input-group {
  @apply flex items-center space-x-2;
}

.color-picker {
  @apply w-10 h-8 border border-gray-300 rounded cursor-pointer;
}

.color-text {
  @apply flex-1 px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.typography-controls,
.spacing-controls {
  @apply space-y-3;
}

.control-group {
  @apply space-y-1;
}

.control-label {
  @apply text-sm text-gray-700;
}

.control-select {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.range-control {
  @apply flex items-center space-x-3;
}

.range-slider {
  @apply flex-1;
}

.range-value {
  @apply text-sm text-gray-600 min-w-[3rem] text-right;
}

.css-editor {
  @apply space-y-2;
}

.css-textarea {
  @apply w-full px-3 py-2 text-sm font-mono border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none;
}

.css-help {
  @apply text-xs text-gray-500;
}

.responsive-controls {
  @apply space-y-3;
}

.device-tabs {
  @apply flex space-x-1 bg-gray-100 rounded-lg p-1;
}

.device-tab {
  @apply flex items-center space-x-1 px-3 py-1 text-sm rounded-md transition-colors;
}

.device-tab.active {
  @apply bg-white text-primary-600 shadow-sm;
}

.device-settings {
  @apply space-y-3;
}
</style>
