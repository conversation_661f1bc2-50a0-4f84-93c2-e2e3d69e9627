<template>
  <div 
    class="enhanced-form-field"
    :class="{ 
      'selected': isSelected,
      'dragging': isDragging,
      'multi-select': multiSelect,
      'field-hidden': !field.is_visible 
    }"
    @click="handleClick"
    @mousedown="handleMouseDown"
    draggable="true"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
  >
    <!-- Selection Checkbox (Multi-select mode) -->
    <div v-if="multiSelect" class="selection-checkbox">
      <input 
        type="checkbox" 
        :checked="isSelected"
        @click.stop
        @change="handleSelectionChange"
        class="form-checkbox"
      />
    </div>
    
    <!-- Field Header -->
    <div class="field-header">
      <div class="field-header-left">
        <div class="drag-handle" @mousedown.stop>
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
          </svg>
        </div>
        
        <div class="field-info">
          <span class="field-type-badge">
            {{ getFieldTypeLabel(field.field_type) }}
          </span>
          
          <div class="field-indicators">
            <span v-if="field.required" class="indicator indicator-required">Required</span>
            <span v-if="!field.is_visible" class="indicator indicator-hidden">Hidden</span>
            <span v-if="field.conditional_logic && Object.keys(field.conditional_logic).length > 0" class="indicator indicator-conditional">Conditional</span>
          </div>
        </div>
      </div>
      
      <div class="field-actions">
        <button 
          @click.stop="moveUp"
          :disabled="index === 0"
          class="field-action-btn"
          title="Move up"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
          </svg>
        </button>
        
        <button 
          @click.stop="moveDown"
          :disabled="isLast"
          class="field-action-btn"
          title="Move down"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <div class="action-separator"></div>
        
        <button 
          @click.stop="duplicateField"
          class="field-action-btn"
          title="Duplicate field"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        </button>
        
        <button 
          @click.stop="deleteField"
          class="field-action-btn field-action-delete"
          title="Delete field"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Field Preview -->
    <div class="field-preview">
      <component 
        :is="getFieldComponent(field.field_type)"
        :field="field"
        :preview="true"
        @field-updated="updateField"
      />
    </div>
    
    <!-- Field Footer -->
    <div v-if="field.help_text || showFieldInfo" class="field-footer">
      <div v-if="field.help_text" class="field-help">
        {{ field.help_text }}
      </div>
      <div v-if="showFieldInfo" class="field-meta">
        <span class="field-name">{{ field.name }}</span>
        <span class="field-order">Order: {{ field.order }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
// Import all field preview components
import TextFieldPreview from './field-previews/TextFieldPreview.vue'
import TextAreaFieldPreview from './field-previews/TextAreaFieldPreview.vue'
import EmailFieldPreview from './field-previews/EmailFieldPreview.vue'
import NumberFieldPreview from './field-previews/NumberFieldPreview.vue'
import DateFieldPreview from './field-previews/DateFieldPreview.vue'
import SelectFieldPreview from './field-previews/SelectFieldPreview.vue'
import RadioFieldPreview from './field-previews/RadioFieldPreview.vue'
import CheckboxFieldPreview from './field-previews/CheckboxFieldPreview.vue'
import FileFieldPreview from './field-previews/FileFieldPreview.vue'
import SectionFieldPreview from './field-previews/SectionFieldPreview.vue'
import HtmlFieldPreview from './field-previews/HtmlFieldPreview.vue'
import RichTextFieldPreview from './field-previews/RichTextFieldPreview.vue'
import SignatureFieldPreview from './field-previews/SignatureFieldPreview.vue'
import ColorFieldPreview from './field-previews/ColorFieldPreview.vue'
import PhoneFieldPreview from './field-previews/PhoneFieldPreview.vue'
import AddressFieldPreview from './field-previews/AddressFieldPreview.vue'
import MatrixFieldPreview from './field-previews/MatrixFieldPreview.vue'

export default {
  name: 'EnhancedFormField',
  components: {
    TextFieldPreview,
    TextAreaFieldPreview,
    EmailFieldPreview,
    NumberFieldPreview,
    DateFieldPreview,
    SelectFieldPreview,
    RadioFieldPreview,
    CheckboxFieldPreview,
    FileFieldPreview,
    SectionFieldPreview,
    HtmlFieldPreview,
    RichTextFieldPreview,
    SignatureFieldPreview,
    ColorFieldPreview,
    PhoneFieldPreview,
    AddressFieldPreview,
    MatrixFieldPreview
  },
  props: {
    field: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    isDragging: {
      type: Boolean,
      default: false
    },
    multiSelect: {
      type: Boolean,
      default: false
    },
    isLast: {
      type: Boolean,
      default: false
    },
    showFieldInfo: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'field-selected', 
    'field-updated', 
    'field-deleted', 
    'field-duplicated',
    'field-moved',
    'drag-start',
    'drag-end'
  ],
  setup(props, { emit }) {
    const handleClick = (event) => {
      emit('field-selected', props.field, event)
    }
    
    const handleMouseDown = (event) => {
      // Prevent text selection when clicking on field
      event.preventDefault()
    }
    
    const handleSelectionChange = (event) => {
      emit('field-selected', props.field, { 
        ctrlKey: true, 
        target: { checked: event.target.checked } 
      })
    }
    
    const updateField = (fieldData) => {
      emit('field-updated', { ...props.field, ...fieldData })
    }
    
    const deleteField = () => {
      emit('field-deleted', props.field.id)
    }
    
    const duplicateField = () => {
      emit('field-duplicated', props.field)
    }
    
    const moveUp = () => {
      if (props.index > 0) {
        emit('field-moved', props.field.id, props.index - 1)
      }
    }
    
    const moveDown = () => {
      emit('field-moved', props.field.id, props.index + 1)
    }
    
    const handleDragStart = (event) => {
      event.dataTransfer.setData('text/plain', props.field.field_type)
      event.dataTransfer.effectAllowed = 'move'
      emit('drag-start', props.field.id)
    }
    
    const handleDragEnd = () => {
      emit('drag-end')
    }
    
    const getFieldTypeLabel = (fieldType) => {
      const labels = {
        text: 'Text',
        textarea: 'Text Area',
        email: 'Email',
        url: 'URL',
        number: 'Number',
        date: 'Date',
        time: 'Time',
        datetime: 'Date & Time',
        select: 'Dropdown',
        radio: 'Radio',
        checkbox: 'Checkbox',
        checkbox_single: 'Single Checkbox',
        file: 'File',
        image: 'Image',
        rating: 'Rating',
        slider: 'Slider',
        signature: 'Signature',
        rich_text: 'Rich Text',
        color: 'Color',
        phone: 'Phone',
        address: 'Address',
        matrix: 'Matrix',
        section: 'Section',
        html: 'HTML',
        hidden: 'Hidden'
      }
      
      return labels[fieldType] || fieldType
    }
    
    const getFieldComponent = (fieldType) => {
      const components = {
        text: 'TextFieldPreview',
        textarea: 'TextAreaFieldPreview',
        email: 'EmailFieldPreview',
        url: 'TextFieldPreview',
        password: 'TextFieldPreview',
        number: 'NumberFieldPreview',
        integer: 'NumberFieldPreview',
        decimal: 'NumberFieldPreview',
        date: 'DateFieldPreview',
        time: 'DateFieldPreview',
        datetime: 'DateFieldPreview',
        select: 'SelectFieldPreview',
        radio: 'RadioFieldPreview',
        checkbox: 'CheckboxFieldPreview',
        checkbox_single: 'CheckboxFieldPreview',
        file: 'FileFieldPreview',
        image: 'FileFieldPreview',
        rating: 'NumberFieldPreview',
        slider: 'NumberFieldPreview',
        signature: 'SignatureFieldPreview',
        rich_text: 'RichTextFieldPreview',
        color: 'ColorFieldPreview',
        phone: 'PhoneFieldPreview',
        address: 'AddressFieldPreview',
        matrix: 'MatrixFieldPreview',
        section: 'SectionFieldPreview',
        html: 'HtmlFieldPreview',
        hidden: 'TextFieldPreview'
      }
      
      return components[fieldType] || 'TextFieldPreview'
    }
    
    return {
      handleClick,
      handleMouseDown,
      handleSelectionChange,
      updateField,
      deleteField,
      duplicateField,
      moveUp,
      moveDown,
      handleDragStart,
      handleDragEnd,
      getFieldTypeLabel,
      getFieldComponent
    }
  }
}
</script>

<style scoped>
.enhanced-form-field {
  @apply relative bg-white border border-gray-200 rounded-lg transition-all duration-200 cursor-pointer;
}

.enhanced-form-field:hover {
  @apply border-gray-300 shadow-sm;
}

.enhanced-form-field.selected {
  @apply border-primary-500 ring-2 ring-primary-100;
}

.enhanced-form-field.dragging {
  @apply opacity-50 transform rotate-2;
}

.enhanced-form-field.multi-select {
  @apply pl-10;
}

.enhanced-form-field.field-hidden {
  @apply opacity-60;
}

.selection-checkbox {
  @apply absolute left-3 top-4 z-10;
}

.field-header {
  @apply flex items-center justify-between p-3 border-b border-gray-100;
}

.field-header-left {
  @apply flex items-center space-x-3;
}

.drag-handle {
  @apply opacity-0 transition-opacity cursor-move;
}

.enhanced-form-field:hover .drag-handle {
  @apply opacity-100;
}

.field-info {
  @apply flex items-center space-x-2;
}

.field-type-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full;
}

.field-indicators {
  @apply flex items-center space-x-1;
}

.indicator {
  @apply inline-flex items-center px-1.5 py-0.5 text-xs font-medium rounded;
}

.indicator-required {
  @apply bg-red-100 text-red-700;
}

.indicator-hidden {
  @apply bg-gray-100 text-gray-600;
}

.indicator-conditional {
  @apply bg-blue-100 text-blue-700;
}

.field-actions {
  @apply flex items-center space-x-1 opacity-0 transition-opacity;
}

.enhanced-form-field:hover .field-actions {
  @apply opacity-100;
}

.field-action-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors disabled:opacity-30 disabled:cursor-not-allowed;
}

.field-action-delete {
  @apply hover:text-red-600;
}

.action-separator {
  @apply w-px h-4 bg-gray-300 mx-1;
}

.field-preview {
  @apply p-3 pointer-events-none;
}

.field-footer {
  @apply px-3 pb-3 space-y-2;
}

.field-help {
  @apply text-sm text-gray-500;
}

.field-meta {
  @apply flex items-center justify-between text-xs text-gray-400;
}

.field-name {
  @apply font-mono;
}
</style>
