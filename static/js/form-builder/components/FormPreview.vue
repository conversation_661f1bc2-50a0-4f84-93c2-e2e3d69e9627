<template>
  <div class="form-preview">
    <!-- Preview Header -->
    <div class="preview-header">
      <div class="preview-title-section">
        <h3 class="preview-title">Live Preview</h3>
        <div class="preview-info">
          <span class="field-count">{{ fields.length }} fields</span>
          <span class="separator">•</span>
          <span class="page-count">{{ pages.length }} page{{ pages.length !== 1 ? 's' : '' }}</span>
        </div>
      </div>

      <div class="preview-controls">
        <!-- Device Toggle -->
        <div class="device-toggle">
          <button
            v-for="device in devices"
            :key="device.id"
            @click="setPreviewDevice(device.id)"
            class="device-btn"
            :class="{ 'active': previewDevice === device.id }"
            :title="device.name"
          >
            <component :is="device.icon" class="w-4 h-4" />
          </button>
        </div>

        <!-- Preview Mode Toggle -->
        <div class="mode-toggle">
          <button
            @click="previewMode = 'static'"
            class="mode-btn"
            :class="{ 'active': previewMode === 'static' }"
          >
            Static
          </button>
          <button
            @click="previewMode = 'interactive'"
            class="mode-btn"
            :class="{ 'active': previewMode === 'interactive' }"
          >
            Interactive
          </button>
        </div>

        <!-- Fullscreen Toggle -->
        <button
          @click="toggleFullscreen"
          class="fullscreen-btn"
          :title="isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'"
        >
          <svg v-if="!isFullscreen" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
          </svg>
          <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 9V4.5M9 9H4.5M9 9L3.5 3.5M15 9h4.5M15 9V4.5M15 9l5.5-5.5M9 15v4.5M9 15H4.5M9 15l-5.5 5.5M15 15h4.5M15 15v4.5m0 0l5.5 5.5"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Preview Container -->
    <div
      class="preview-container"
      :class="[
        `device-${previewDevice}`,
        { 'fullscreen': isFullscreen }
      ]"
    >
      <div class="preview-frame">
        <!-- Form Preview -->
        <div
          class="form-preview-content"
          :style="getFormStyles()"
        >
          <!-- Form Header -->
          <div v-if="form.title || form.description" class="form-header">
            <h1 v-if="form.title" class="form-title">{{ form.title }}</h1>
            <p v-if="form.description" class="form-description">{{ form.description }}</p>
          </div>

          <!-- Form Fields -->
          <div class="form-fields">
            <FormFieldRenderer
              v-for="field in visibleFields"
              :key="field.id"
              :field="field"
              :mode="previewMode"
              :device="previewDevice"
              :form-data="formData"
              @field-changed="handleFieldChange"
              class="field-renderer"
            />
          </div>

          <!-- Empty State -->
          <div v-if="visibleFields.length === 0" class="empty-state">
            <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="empty-title">No Fields Added</h3>
            <p class="empty-description">Add some fields to see the form preview</p>
          </div>

          <!-- Submit Button -->
          <div v-if="visibleFields.length > 0" class="form-actions">
            <button
              @click="submitForm"
              class="submit-btn"
              :disabled="previewMode === 'static'"
            >
              {{ form.submit_button_text || 'Submit' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Success Message -->
    <div v-if="submitted" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
      <div class="flex">
        <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div class="ml-3">
          <p class="text-sm font-medium text-green-800">
            {{ form.settings?.success_message || 'Thank you for your submission!' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import TextFieldRenderer from './field-renderers/TextFieldRenderer.vue'
import TextAreaFieldRenderer from './field-renderers/TextAreaFieldRenderer.vue'
import EmailFieldRenderer from './field-renderers/EmailFieldRenderer.vue'
import NumberFieldRenderer from './field-renderers/NumberFieldRenderer.vue'
import DateFieldRenderer from './field-renderers/DateFieldRenderer.vue'
import SelectFieldRenderer from './field-renderers/SelectFieldRenderer.vue'
import RadioFieldRenderer from './field-renderers/RadioFieldRenderer.vue'
import CheckboxFieldRenderer from './field-renderers/CheckboxFieldRenderer.vue'
import FileFieldRenderer from './field-renderers/FileFieldRenderer.vue'
import SectionFieldRenderer from './field-renderers/SectionFieldRenderer.vue'
import HtmlFieldRenderer from './field-renderers/HtmlFieldRenderer.vue'

export default {
  name: 'FormPreview',
  components: {
    TextFieldRenderer,
    TextAreaFieldRenderer,
    EmailFieldRenderer,
    NumberFieldRenderer,
    DateFieldRenderer,
    SelectFieldRenderer,
    RadioFieldRenderer,
    CheckboxFieldRenderer,
    FileFieldRenderer,
    SectionFieldRenderer,
    HtmlFieldRenderer
  },
  props: {
    form: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      required: true
    }
  },
  setup(props) {
    const formData = reactive({})
    const errors = reactive({})
    const submitting = ref(false)
    const submitted = ref(false)
    
    // Initialize form data
    props.fields.forEach(field => {
      if (field.field_type !== 'section' && field.field_type !== 'html') {
        formData[field.name] = getDefaultValue(field)
      }
    })
    
    const visibleFields = computed(() => {
      return props.fields
        .filter(field => field.is_visible)
        .sort((a, b) => a.order - b.order)
    })
    
    const updateFieldValue = (fieldName, value) => {
      formData[fieldName] = value
      // Clear error when user starts typing
      if (errors[fieldName]) {
        delete errors[fieldName]
      }
    }
    
    const getDefaultValue = (field) => {
      switch (field.field_type) {
        case 'checkbox':
          return []
        case 'checkbox_single':
          return false
        case 'number':
        case 'integer':
        case 'decimal':
          return null
        case 'file':
        case 'image':
          return null
        default:
          return ''
      }
    }
    
    const getFieldComponent = (fieldType) => {
      const components = {
        text: 'TextFieldRenderer',
        textarea: 'TextAreaFieldRenderer',
        email: 'EmailFieldRenderer',
        url: 'TextFieldRenderer',
        password: 'TextFieldRenderer',
        number: 'NumberFieldRenderer',
        integer: 'NumberFieldRenderer',
        decimal: 'NumberFieldRenderer',
        date: 'DateFieldRenderer',
        time: 'DateFieldRenderer',
        datetime: 'DateFieldRenderer',
        select: 'SelectFieldRenderer',
        radio: 'RadioFieldRenderer',
        checkbox: 'CheckboxFieldRenderer',
        checkbox_single: 'CheckboxFieldRenderer',
        file: 'FileFieldRenderer',
        image: 'FileFieldRenderer',
        section: 'SectionFieldRenderer',
        html: 'HtmlFieldRenderer',
        hidden: 'TextFieldRenderer'
      }
      
      return components[fieldType] || 'TextFieldRenderer'
    }
    
    const getFieldWrapperClass = (field) => {
      const baseClass = 'field-wrapper'
      const widthClasses = {
        full: 'w-full',
        half: 'w-1/2',
        third: 'w-1/3',
        quarter: 'w-1/4'
      }
      
      return `${baseClass} ${widthClasses[field.width] || widthClasses.full}`
    }
    
    const validateForm = () => {
      const newErrors = {}
      
      props.fields.forEach(field => {
        if (!field.is_visible || field.field_type === 'section' || field.field_type === 'html') {
          return
        }
        
        const value = formData[field.name]
        
        // Required field validation
        if (field.required) {
          if (field.field_type === 'checkbox' && (!value || value.length === 0)) {
            newErrors[field.name] = 'This field is required'
          } else if (field.field_type === 'checkbox_single' && !value) {
            newErrors[field.name] = 'This field is required'
          } else if (!value || (typeof value === 'string' && value.trim() === '')) {
            newErrors[field.name] = 'This field is required'
          }
        }
        
        // Field-specific validation
        if (value && typeof value === 'string' && value.trim() !== '') {
          switch (field.field_type) {
            case 'email':
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              if (!emailRegex.test(value)) {
                newErrors[field.name] = 'Please enter a valid email address'
              }
              break
              
            case 'url':
              try {
                new URL(value)
              } catch {
                newErrors[field.name] = 'Please enter a valid URL'
              }
              break
              
            case 'text':
            case 'textarea':
              const maxLength = field.properties?.max_length
              if (maxLength && value.length > maxLength) {
                newErrors[field.name] = `Maximum ${maxLength} characters allowed`
              }
              break
              
            case 'number':
            case 'integer':
            case 'decimal':
              const numValue = parseFloat(value)
              if (isNaN(numValue)) {
                newErrors[field.name] = 'Please enter a valid number'
              } else {
                const minValue = field.properties?.min_value
                const maxValue = field.properties?.max_value
                
                if (minValue !== null && numValue < minValue) {
                  newErrors[field.name] = `Minimum value is ${minValue}`
                }
                if (maxValue !== null && numValue > maxValue) {
                  newErrors[field.name] = `Maximum value is ${maxValue}`
                }
              }
              break
          }
        }
      })
      
      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }
    
    const handleSubmit = async () => {
      if (!validateForm()) {
        return
      }
      
      submitting.value = true
      
      try {
        // Simulate form submission
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        submitted.value = true
        
        // Reset form after successful submission
        setTimeout(() => {
          submitted.value = false
          Object.keys(formData).forEach(key => {
            formData[key] = getDefaultValue(props.fields.find(f => f.name === key))
          })
        }, 3000)
        
      } catch (error) {
        console.error('Form submission error:', error)
      } finally {
        submitting.value = false
      }
    }
    
    return {
      formData,
      errors,
      submitting,
      submitted,
      visibleFields,
      updateFieldValue,
      getFieldComponent,
      getFieldWrapperClass,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.form-preview {
  @apply max-w-2xl mx-auto;
}

.form-preview-header {
  @apply mb-8;
}

.field-wrapper {
  @apply mb-6;
}
</style>
