<template>
  <div class="form-builder-main">
    <!-- Header -->
    <div class="builder-header">
      <div class="header-left">
        <h1 class="builder-title">{{ form.name || 'Untitled Form' }}</h1>
        <div class="builder-status">
          <span class="status-indicator" :class="statusClass">{{ form.status || 'Draft' }}</span>
          <span class="field-count">{{ fields.length }} fields</span>
        </div>
      </div>
      
      <div class="header-actions">
        <button @click="togglePreview" class="action-btn">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          Preview
        </button>
        
        <button @click="saveForm" :disabled="saving" class="action-btn action-btn-primary">
          <svg v-if="saving" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          {{ saving ? 'Saving...' : 'Save' }}
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="builder-content">
      <!-- Sidebar -->
      <div class="builder-sidebar">
        <div class="sidebar-tabs">
          <button 
            v-for="tab in sidebarTabs" 
            :key="tab.id"
            @click="activeSidebarTab = tab.id"
            class="sidebar-tab"
            :class="{ 'active': activeSidebarTab === tab.id }"
          >
            <svg v-if="tab.id === 'fields'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <svg v-else-if="tab.id === 'design'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
            </svg>
            <svg v-else-if="tab.id === 'templates'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <svg v-else-if="tab.id === 'settings'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            {{ tab.label }}
          </button>
        </div>
        
        <div class="sidebar-content">
          <!-- Field Palette -->
          <FieldPalette 
            v-if="activeSidebarTab === 'fields'"
            @field-selected="addField"
          />
          
          <!-- Form Customization -->
          <FormCustomizationPanel 
            v-if="activeSidebarTab === 'design'"
            :form="form"
            @customization-updated="updateCustomization"
          />
          
          <!-- Layout Templates -->
          <FormLayoutTemplates 
            v-if="activeSidebarTab === 'templates'"
            :current-form="form"
            @template-selected="applyTemplate"
          />
          
          <!-- Form Settings -->
          <FormSettingsPanel 
            v-if="activeSidebarTab === 'settings'"
            :form="form"
            @settings-updated="updateFormSettings"
          />
        </div>
      </div>

      <!-- Canvas Area -->
      <div class="builder-canvas">
        <div class="canvas-header">
          <div class="canvas-tools">
            <button 
              @click="undo" 
              :disabled="!canUndo"
              class="tool-btn"
              title="Undo (Ctrl+Z)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
              </svg>
            </button>
            
            <button 
              @click="redo" 
              :disabled="!canRedo"
              class="tool-btn"
              title="Redo (Ctrl+Y)"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6"></path>
              </svg>
            </button>
            
            <div class="tool-separator"></div>
            
            <button 
              @click="clearForm" 
              class="tool-btn tool-btn-danger"
              title="Clear all fields"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
          
          <div class="canvas-info">
            <span class="info-text">{{ fields.length }} field{{ fields.length !== 1 ? 's' : '' }}</span>
          </div>
        </div>
        
        <!-- Form Canvas -->
        <div class="canvas-container" :style="canvasStyles">
          <FormCanvas
            :fields="fields"
            :selected-field="selectedField"
            @field-selected="selectField"
            @field-updated="updateField"
            @field-deleted="deleteField"
            @field-duplicated="duplicateField"
            @field-added="addFieldAtPosition"
            @fields-reordered="reorderFields"
          />
        </div>
      </div>

      <!-- Properties Panel -->
      <div class="builder-properties" v-if="selectedField">
        <div class="properties-header">
          <h3 class="properties-title">Field Properties</h3>
          <button @click="closeProperties" class="close-btn">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <PropertyPanel
          :field="selectedField"
          :available-fields="fields"
          @field-updated="updateField"
          @field-deleted="deleteField"
        />
      </div>
    </div>

    <!-- Preview Modal -->
    <div v-if="showPreview" class="preview-modal">
      <div class="modal-backdrop" @click="closePreview"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">Form Preview</h3>
          <button @click="closePreview" class="modal-close">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="modal-body">
          <FormPreview
            :form="form"
            :fields="fields"
            :customization="customization"
            @preview-submitted="handlePreviewSubmit"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useFormBuilder } from '../composables/useFormBuilder'
import { useFormStyling } from '../composables/useFormStyling'
import { useKeyboardShortcuts } from '../composables/useKeyboardShortcuts'

import FieldPalette from './FieldPalette.vue'
import FormCustomizationPanel from './FormCustomizationPanel.vue'
import FormLayoutTemplates from './FormLayoutTemplates.vue'
import FormSettingsPanel from './FormSettingsPanel.vue'
import FormCanvas from './FormCanvas.vue'
import PropertyPanel from './PropertyPanel.vue'
import FormPreview from './FormPreview.vue'

export default {
  name: 'FormBuilderMain',
  components: {
    FieldPalette,
    FormCustomizationPanel,
    FormLayoutTemplates,
    FormSettingsPanel,
    FormCanvas,
    PropertyPanel,
    FormPreview
  },
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const activeSidebarTab = ref('fields')
    const showPreview = ref(false)
    const saving = ref(false)
    
    // Form builder composable
    const formBuilder = useFormBuilder(props.formData)
    const { 
      form, 
      fields, 
      selectedField, 
      canUndo, 
      canRedo,
      addField,
      updateField,
      deleteField,
      duplicateField,
      selectField,
      undo,
      redo,
      saveForm: saveFormData
    } = formBuilder
    
    // Form styling composable
    const styling = useFormStyling(props.formData.customization)
    const { customization, applyStylesToDOM } = styling
    
    // Keyboard shortcuts
    useKeyboardShortcuts(formBuilder)
    
    const sidebarTabs = ref([
      { id: 'fields', label: 'Fields' },
      { id: 'design', label: 'Design' },
      { id: 'templates', label: 'Templates' },
      { id: 'settings', label: 'Settings' }
    ])
    
    const statusClass = computed(() => {
      const status = form.value.status?.toLowerCase()
      return {
        'status-draft': status === 'draft',
        'status-published': status === 'published',
        'status-archived': status === 'archived'
      }
    })
    
    const canvasStyles = computed(() => {
      return {
        '--form-primary-color': customization.value.colors.primary,
        '--form-background-color': customization.value.colors.background,
        '--form-text-color': customization.value.colors.text,
        '--form-font-family': customization.value.typography.fontFamily,
        '--form-font-size': `${customization.value.typography.fontSize}px`,
        '--form-field-spacing': `${customization.value.spacing.fieldSpacing}px`,
        '--form-padding': `${customization.value.spacing.formPadding}px`
      }
    })
    
    const addFieldAtPosition = (fieldType, position) => {
      addField(fieldType, position)
    }
    
    const reorderFields = (newOrder) => {
      // Handle field reordering
      console.log('Reorder fields:', newOrder)
    }
    
    const updateCustomization = (newCustomization) => {
      Object.assign(customization.value, newCustomization)
      applyStylesToDOM()
    }
    
    const updateFormSettings = (settings) => {
      Object.assign(form.value, settings)
    }
    
    const applyTemplate = (template) => {
      console.log('Apply template:', template)
      // Implement template application logic
    }
    
    const clearForm = () => {
      if (confirm('Are you sure you want to clear all fields? This action cannot be undone.')) {
        fields.value = []
      }
    }
    
    const closeProperties = () => {
      selectedField.value = null
    }
    
    const togglePreview = () => {
      showPreview.value = !showPreview.value
    }
    
    const closePreview = () => {
      showPreview.value = false
    }
    
    const handlePreviewSubmit = (formData) => {
      console.log('Preview form submitted:', formData)
    }
    
    const saveForm = async () => {
      saving.value = true
      try {
        await saveFormData()
        // Show success message
      } catch (error) {
        console.error('Failed to save form:', error)
        // Show error message
      } finally {
        saving.value = false
      }
    }
    
    onMounted(() => {
      applyStylesToDOM()
    })
    
    return {
      activeSidebarTab,
      showPreview,
      saving,
      sidebarTabs,
      form,
      fields,
      selectedField,
      canUndo,
      canRedo,
      customization,
      statusClass,
      canvasStyles,
      addField,
      addFieldAtPosition,
      updateField,
      deleteField,
      duplicateField,
      selectField,
      reorderFields,
      updateCustomization,
      updateFormSettings,
      applyTemplate,
      undo,
      redo,
      clearForm,
      closeProperties,
      togglePreview,
      closePreview,
      handlePreviewSubmit,
      saveForm
    }
  }
}
</script>

<style scoped>
.form-builder-main {
  @apply h-screen flex flex-col bg-gray-50;
}

.builder-header {
  @apply flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200;
}

.header-left {
  @apply space-y-1;
}

.builder-title {
  @apply text-xl font-semibold text-gray-900;
}

.builder-status {
  @apply flex items-center space-x-3 text-sm;
}

.status-indicator {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-draft {
  @apply bg-yellow-100 text-yellow-800;
}

.status-published {
  @apply bg-green-100 text-green-800;
}

.status-archived {
  @apply bg-gray-100 text-gray-800;
}

.field-count {
  @apply text-gray-500;
}

.header-actions {
  @apply flex items-center space-x-2;
}

.action-btn {
  @apply flex items-center space-x-2 px-4 py-2 text-sm font-medium border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.action-btn-primary {
  @apply text-white bg-primary-600 border-primary-600 hover:bg-primary-700;
}

.builder-content {
  @apply flex-1 flex overflow-hidden;
}

.builder-sidebar {
  @apply w-80 bg-white border-r border-gray-200 flex flex-col;
}

.sidebar-tabs {
  @apply flex border-b border-gray-200;
}

.sidebar-tab {
  @apply flex-1 flex items-center justify-center space-x-1 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors;
}

.sidebar-tab.active {
  @apply text-primary-600 border-b-2 border-primary-600 bg-primary-50;
}

.sidebar-content {
  @apply flex-1 overflow-y-auto p-4;
}

.builder-canvas {
  @apply flex-1 flex flex-col;
}

.canvas-header {
  @apply flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200;
}

.canvas-tools {
  @apply flex items-center space-x-1;
}

.tool-btn {
  @apply p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.tool-btn-danger {
  @apply hover:text-red-600 hover:bg-red-50;
}

.tool-separator {
  @apply w-px h-6 bg-gray-300 mx-2;
}

.canvas-info {
  @apply text-sm text-gray-500;
}

.canvas-container {
  @apply flex-1 overflow-y-auto;
}

.builder-properties {
  @apply w-80 bg-white border-l border-gray-200 flex flex-col;
}

.properties-header {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-200;
}

.properties-title {
  @apply text-lg font-medium text-gray-900;
}

.close-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.preview-modal {
  @apply fixed inset-0 z-50 flex items-center justify-center;
}

.modal-backdrop {
  @apply absolute inset-0 bg-black bg-opacity-50;
}

.modal-content {
  @apply relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col;
}

.modal-header {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-close {
  @apply text-gray-400 hover:text-gray-600 transition-colors;
}

.modal-body {
  @apply flex-1 overflow-y-auto;
}
</style>
