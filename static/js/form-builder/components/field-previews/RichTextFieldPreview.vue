<template>
  <div class="rich-text-field-preview">
    <label v-if="field.label" class="field-label">
      {{ field.label }}
      <span v-if="field.required" class="field-required">*</span>
    </label>
    
    <div class="rich-text-editor-preview">
      <!-- Toolbar Preview -->
      <div class="editor-toolbar">
        <div class="toolbar-group">
          <button type="button" class="toolbar-btn" title="Bold">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 4h8a4 4 0 014 4 4 4 0 01-4 4H6z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 12h9a4 4 0 014 4 4 4 0 01-4 4H6z"></path>
            </svg>
          </button>
          <button type="button" class="toolbar-btn" title="Italic">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 4l-2 14M14 4l-2 14"></path>
            </svg>
          </button>
          <button type="button" class="toolbar-btn" title="Underline">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 19h12M8 5v8a4 4 0 008 0V5"></path>
            </svg>
          </button>
        </div>
        
        <div class="toolbar-separator"></div>
        
        <div class="toolbar-group">
          <button type="button" class="toolbar-btn" title="Bullet List">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
          <button type="button" class="toolbar-btn" title="Numbered List">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Editor Content Area -->
      <div class="editor-content">
        <div class="editor-placeholder">
          {{ field.placeholder || 'Enter rich text content...' }}
        </div>
      </div>
    </div>
    
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'RichTextFieldPreview',
  props: {
    field: {
      type: Object,
      required: true
    },
    preview: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.rich-text-field-preview {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.field-required {
  @apply text-red-500;
}

.rich-text-editor-preview {
  @apply border border-gray-300 rounded-md overflow-hidden;
}

.editor-toolbar {
  @apply flex items-center px-3 py-2 bg-gray-50 border-b border-gray-200;
}

.toolbar-group {
  @apply flex items-center space-x-1;
}

.toolbar-separator {
  @apply w-px h-6 bg-gray-300 mx-2;
}

.toolbar-btn {
  @apply p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors;
}

.editor-content {
  @apply p-3 min-h-[120px] bg-white;
}

.editor-placeholder {
  @apply text-gray-400 italic;
}

.field-help {
  @apply text-sm text-gray-500;
}
</style>
