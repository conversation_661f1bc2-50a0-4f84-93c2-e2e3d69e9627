<template>
  <div class="phone-field-preview">
    <label v-if="field.label" class="field-label">
      {{ field.label }}
      <span v-if="field.required" class="field-required">*</span>
    </label>
    
    <div class="phone-input-wrapper">
      <select class="country-select">
        <option value="+1">🇺🇸 +1</option>
        <option value="+44">🇬🇧 +44</option>
        <option value="+33">🇫🇷 +33</option>
        <option value="+49">🇩🇪 +49</option>
        <option value="+81">🇯🇵 +81</option>
      </select>
      <input 
        type="tel" 
        :placeholder="field.placeholder || '(*************'"
        class="phone-input"
      />
    </div>
    
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'PhoneFieldPreview',
  props: {
    field: {
      type: Object,
      required: true
    },
    preview: {
      type: <PERSON>olean,
      default: false
    }
  }
}
</script>

<style scoped>
.phone-field-preview {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.field-required {
  @apply text-red-500;
}

.phone-input-wrapper {
  @apply flex;
}

.country-select {
  @apply px-3 py-2 border border-gray-300 border-r-0 rounded-l-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm bg-gray-50;
}

.phone-input {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm;
}

.field-help {
  @apply text-sm text-gray-500;
}
</style>
