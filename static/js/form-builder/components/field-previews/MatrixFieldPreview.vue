<template>
  <div class="matrix-field-preview">
    <label v-if="field.label" class="field-label">
      {{ field.label }}
      <span v-if="field.required" class="field-required">*</span>
    </label>
    
    <div class="matrix-table">
      <table class="matrix-grid">
        <thead>
          <tr>
            <th class="matrix-header-empty"></th>
            <th v-for="column in matrixColumns" :key="column" class="matrix-header">
              {{ column }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="row in matrixRows" :key="row" class="matrix-row">
            <td class="matrix-row-label">{{ row }}</td>
            <td v-for="column in matrixColumns" :key="`${row}-${column}`" class="matrix-cell">
              <input 
                type="radio" 
                :name="`matrix-${row}`"
                class="matrix-radio"
              />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'MatrixFieldPreview',
  props: {
    field: {
      type: Object,
      required: true
    },
    preview: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const matrixRows = computed(() => {
      return props.field.properties?.rows || ['Question 1', 'Question 2', 'Question 3']
    })
    
    const matrixColumns = computed(() => {
      return props.field.properties?.columns || ['Strongly Disagree', 'Disagree', 'Neutral', 'Agree', 'Strongly Agree']
    })
    
    return {
      matrixRows,
      matrixColumns
    }
  }
}
</script>

<style scoped>
.matrix-field-preview {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.field-required {
  @apply text-red-500;
}

.matrix-table {
  @apply overflow-x-auto;
}

.matrix-grid {
  @apply w-full border border-gray-300 rounded-md overflow-hidden;
}

.matrix-header-empty {
  @apply bg-gray-50 border-b border-gray-300 p-3 w-48;
}

.matrix-header {
  @apply bg-gray-50 border-b border-gray-300 border-l border-gray-300 p-3 text-center text-sm font-medium text-gray-700 min-w-[120px];
}

.matrix-row:nth-child(even) {
  @apply bg-gray-50;
}

.matrix-row-label {
  @apply p-3 text-sm font-medium text-gray-700 border-b border-gray-300 w-48;
}

.matrix-cell {
  @apply p-3 text-center border-b border-gray-300 border-l border-gray-300;
}

.matrix-radio {
  @apply w-4 h-4 text-primary-600 focus:ring-primary-500 border-gray-300;
}

.field-help {
  @apply text-sm text-gray-500;
}
</style>
