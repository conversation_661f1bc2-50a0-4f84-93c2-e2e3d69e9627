<template>
  <div class="signature-field-preview">
    <label v-if="field.label" class="field-label">
      {{ field.label }}
      <span v-if="field.required" class="field-required">*</span>
    </label>
    
    <div class="signature-pad-preview">
      <div class="signature-canvas">
        <div class="signature-placeholder">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
          </svg>
          <span class="placeholder-text">Click to sign</span>
        </div>
      </div>
      
      <div class="signature-controls">
        <button type="button" class="signature-btn signature-btn-clear">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Clear
        </button>
      </div>
    </div>
    
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'SignatureFieldPreview',
  props: {
    field: {
      type: Object,
      required: true
    },
    preview: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.signature-field-preview {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.field-required {
  @apply text-red-500;
}

.signature-pad-preview {
  @apply border border-gray-300 rounded-md overflow-hidden;
}

.signature-canvas {
  @apply h-32 bg-gray-50 flex items-center justify-center border-b border-gray-200;
}

.signature-placeholder {
  @apply flex flex-col items-center space-y-2 text-gray-400;
}

.placeholder-text {
  @apply text-sm;
}

.signature-controls {
  @apply p-2 bg-white flex justify-end;
}

.signature-btn {
  @apply flex items-center space-x-1 px-3 py-1 text-sm rounded transition-colors;
}

.signature-btn-clear {
  @apply text-gray-600 hover:text-red-600 hover:bg-red-50;
}

.field-help {
  @apply text-sm text-gray-500;
}
</style>
