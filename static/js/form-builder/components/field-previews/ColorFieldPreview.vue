<template>
  <div class="color-field-preview">
    <label v-if="field.label" class="field-label">
      {{ field.label }}
      <span v-if="field.required" class="field-required">*</span>
    </label>
    
    <div class="color-input-wrapper">
      <div class="color-preview" :style="{ backgroundColor: selectedColor }"></div>
      <input 
        type="text" 
        :value="selectedColor"
        :placeholder="field.placeholder || '#000000'"
        class="color-text-input"
        readonly
      />
      <button type="button" class="color-picker-btn">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3V1"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 3V1M19 7h2M19 11h2M19 15h2"></path>
          <circle cx="17" cy="9" r="1"></circle>
          <circle cx="17" cy="13" r="1"></circle>
          <circle cx="17" cy="17" r="1"></circle>
        </svg>
      </button>
    </div>
    
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'ColorFieldPreview',
  props: {
    field: {
      type: Object,
      required: true
    },
    preview: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const selectedColor = ref(props.field.properties?.default_color || '#3B82F6')
    
    return {
      selectedColor
    }
  }
}
</script>

<style scoped>
.color-field-preview {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.field-required {
  @apply text-red-500;
}

.color-input-wrapper {
  @apply flex items-center space-x-2;
}

.color-preview {
  @apply w-10 h-10 border border-gray-300 rounded-md shadow-sm;
}

.color-text-input {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm;
}

.color-picker-btn {
  @apply p-2 border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-gray-600;
}

.field-help {
  @apply text-sm text-gray-500;
}
</style>
