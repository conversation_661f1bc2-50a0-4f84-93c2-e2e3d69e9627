<template>
  <div class="address-field-preview">
    <label v-if="field.label" class="field-label">
      {{ field.label }}
      <span v-if="field.required" class="field-required">*</span>
    </label>
    
    <div class="address-fields">
      <div class="address-row">
        <input 
          type="text" 
          placeholder="Street Address"
          class="address-input full-width"
        />
      </div>
      
      <div class="address-row">
        <input 
          type="text" 
          placeholder="Apartment, suite, etc. (optional)"
          class="address-input full-width"
        />
      </div>
      
      <div class="address-row grid-3">
        <input 
          type="text" 
          placeholder="City"
          class="address-input"
        />
        <select class="address-select">
          <option value="">State</option>
          <option value="CA">California</option>
          <option value="NY">New York</option>
          <option value="TX">Texas</option>
        </select>
        <input 
          type="text" 
          placeholder="ZIP Code"
          class="address-input"
        />
      </div>
      
      <div class="address-row">
        <select class="address-select full-width">
          <option value="">Country</option>
          <option value="US">United States</option>
          <option value="CA">Canada</option>
          <option value="GB">United Kingdom</option>
        </select>
      </div>
    </div>
    
    <div v-if="field.help_text" class="field-help">
      {{ field.help_text }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'AddressFieldPreview',
  props: {
    field: {
      type: Object,
      required: true
    },
    preview: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.address-field-preview {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-medium text-gray-700;
}

.field-required {
  @apply text-red-500;
}

.address-fields {
  @apply space-y-3;
}

.address-row {
  @apply flex space-x-2;
}

.address-row.grid-3 {
  @apply grid grid-cols-3 gap-2;
}

.address-input {
  @apply px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm;
}

.address-select {
  @apply px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm bg-white;
}

.full-width {
  @apply w-full;
}

.field-help {
  @apply text-sm text-gray-500;
}
</style>
