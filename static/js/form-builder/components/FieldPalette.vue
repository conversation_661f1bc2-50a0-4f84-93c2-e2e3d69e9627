<template>
  <div class="field-palette">
    <div class="field-palette-header">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Form Fields</h3>

      <!-- Search and Filter -->
      <div class="palette-search mb-4">
        <div class="search-input-wrapper">
          <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search fields..."
            class="search-input"
          />
          <button
            v-if="searchQuery"
            @click="clearSearch"
            class="clear-search"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="filter-tabs">
          <button
            v-for="category in categories"
            :key="category.id"
            @click="activeCategory = category.id"
            class="filter-tab"
            :class="{ 'active': activeCategory === category.id }"
          >
            {{ category.name }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Filtered Fields -->
    <div class="field-palette-content">
      <div v-if="filteredFields.length === 0" class="empty-results">
        <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <p class="empty-text">No fields found</p>
        <p class="empty-subtext">Try adjusting your search or filter</p>
      </div>

      <div v-else class="filtered-fields space-y-2">
        <div
          v-for="field in filteredFields"
          :key="field.type"
          class="field-palette-item"
          @click="selectField(field.type)"
          draggable="true"
          @dragstart="onDragStart($event, field.type)"
        >
          <component :is="field.icon" class="field-palette-icon" />
          <div class="field-info">
            <div class="field-palette-label">{{ field.label }}</div>
            <div class="field-description">{{ field.description }}</div>
            <div class="field-category">{{ field.category }}</div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { ref, computed } from 'vue'
import * as Icons from '../icons'

export default {
  name: 'FieldPalette',
  emits: ['field-selected'],
  components: {
    ...Icons
  },
  setup(props, { emit }) {
    const searchQuery = ref('')
    const activeCategory = ref('all')

    const categories = ref([
      { id: 'all', name: 'All' },
      { id: 'basic', name: 'Basic' },
      { id: 'selection', name: 'Selection' },
      { id: 'advanced', name: 'Advanced' },
      { id: 'layout', name: 'Layout' }
    ])
    const basicFields = ref([
      {
        type: 'text',
        label: 'Text Input',
        description: 'Single line text input',
        icon: 'TextIcon'
      },
      {
        type: 'textarea',
        label: 'Text Area',
        description: 'Multi-line text input',
        icon: 'TextAreaIcon'
      },
      {
        type: 'email',
        label: 'Email',
        description: 'Email address input',
        icon: 'EmailIcon'
      },
      {
        type: 'url',
        label: 'URL',
        description: 'Website URL input',
        icon: 'UrlIcon'
      },
      {
        type: 'number',
        label: 'Number',
        description: 'Numeric input',
        icon: 'NumberIcon'
      },
      {
        type: 'date',
        label: 'Date',
        description: 'Date picker',
        icon: 'DateIcon'
      },
      {
        type: 'time',
        label: 'Time',
        description: 'Time picker',
        icon: 'TimeIcon'
      }
    ])
    
    const selectionFields = ref([
      {
        type: 'select',
        label: 'Dropdown',
        description: 'Dropdown selection',
        icon: 'SelectIcon'
      },
      {
        type: 'radio',
        label: 'Radio Buttons',
        description: 'Single choice selection',
        icon: 'RadioIcon'
      },
      {
        type: 'checkbox',
        label: 'Checkboxes',
        description: 'Multiple choice selection',
        icon: 'CheckboxIcon'
      },
      {
        type: 'checkbox_single',
        label: 'Single Checkbox',
        description: 'Yes/No checkbox',
        icon: 'CheckboxSingleIcon'
      }
    ])
    
    const advancedFields = ref([
      {
        type: 'file',
        label: 'File Upload',
        description: 'File upload field',
        icon: 'FileIcon'
      },
      {
        type: 'image',
        label: 'Image Upload',
        description: 'Image upload field',
        icon: 'ImageIcon'
      },
      {
        type: 'signature',
        label: 'Signature',
        description: 'Digital signature pad',
        icon: 'SignatureIcon'
      },
      {
        type: 'rich_text',
        label: 'Rich Text Editor',
        description: 'WYSIWYG text editor',
        icon: 'RichTextIcon'
      },
      {
        type: 'rating',
        label: 'Rating',
        description: 'Star rating field',
        icon: 'RatingIcon'
      },
      {
        type: 'slider',
        label: 'Slider',
        description: 'Range slider input',
        icon: 'SliderIcon'
      },
      {
        type: 'color',
        label: 'Color Picker',
        description: 'Color selection field',
        icon: 'ColorIcon'
      },
      {
        type: 'phone',
        label: 'Phone Number',
        description: 'Phone number with validation',
        icon: 'PhoneIcon'
      },
      {
        type: 'address',
        label: 'Address',
        description: 'Multi-line address field',
        icon: 'AddressIcon'
      },
      {
        type: 'matrix',
        label: 'Matrix/Grid',
        description: 'Matrix of questions',
        icon: 'MatrixIcon'
      }
    ])
    
    const layoutFields = ref([
      {
        type: 'section',
        label: 'Section Header',
        description: 'Section divider with title',
        icon: 'SectionIcon'
      },
      {
        type: 'html',
        label: 'HTML Content',
        description: 'Custom HTML content',
        icon: 'HtmlIcon'
      },
      {
        type: 'hidden',
        label: 'Hidden Field',
        description: 'Hidden form field',
        icon: 'HiddenIcon'
      }
    ])
    
    // Computed filtered fields
    const allFields = computed(() => [
      ...basicFields.value.map(f => ({ ...f, category: 'basic' })),
      ...selectionFields.value.map(f => ({ ...f, category: 'selection' })),
      ...advancedFields.value.map(f => ({ ...f, category: 'advanced' })),
      ...layoutFields.value.map(f => ({ ...f, category: 'layout' }))
    ])

    const filteredFields = computed(() => {
      let fields = allFields.value

      // Filter by category
      if (activeCategory.value !== 'all') {
        fields = fields.filter(field => field.category === activeCategory.value)
      }

      // Filter by search query
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        fields = fields.filter(field =>
          field.label.toLowerCase().includes(query) ||
          field.description.toLowerCase().includes(query) ||
          field.type.toLowerCase().includes(query)
        )
      }

      return fields
    })

    const clearSearch = () => {
      searchQuery.value = ''
    }

    const selectField = (fieldType) => {
      emit('field-selected', fieldType)
    }

    const onDragStart = (event, fieldType) => {
      event.dataTransfer.setData('text/plain', fieldType)
      event.dataTransfer.effectAllowed = 'copy'
    }

    return {
      searchQuery,
      activeCategory,
      categories,
      basicFields,
      selectionFields,
      advancedFields,
      layoutFields,
      allFields,
      filteredFields,
      clearSearch,
      selectField,
      onDragStart
    }
  }
}
</script>

<style scoped>
.field-palette {
  @apply p-4;
}

.field-palette-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.palette-search {
  @apply space-y-3;
}

.search-input-wrapper {
  @apply relative;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400;
}

.search-input {
  @apply w-full pl-10 pr-10 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.clear-search {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors;
}

.filter-tabs {
  @apply flex flex-wrap gap-1;
}

.filter-tab {
  @apply px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors;
}

.filter-tab.active {
  @apply text-primary-600 bg-primary-100;
}

.field-palette-content {
  @apply space-y-4;
}

.empty-results {
  @apply text-center py-8;
}

.empty-icon {
  @apply w-12 h-12 mx-auto text-gray-400 mb-3;
}

.empty-text {
  @apply text-sm font-medium text-gray-900 mb-1;
}

.empty-subtext {
  @apply text-xs text-gray-500;
}

.filtered-fields {
  @apply space-y-2;
}

.field-info {
  @apply flex-1;
}

.field-description {
  @apply text-xs text-gray-500;
}

.field-category {
  @apply text-xs text-primary-600 font-medium capitalize mt-1;
}

.field-palette-section {
  @apply mb-6;
}

.field-palette-title {
  @apply text-sm font-semibold text-gray-900 mb-3;
}

.field-palette-item {
  @apply flex items-start p-3 bg-gray-50 rounded-md cursor-pointer hover:bg-gray-100 transition-colors;
}

.field-palette-item:hover {
  @apply shadow-sm;
}

.field-palette-item.dragging {
  @apply opacity-50;
}

.field-palette-icon {
  @apply w-5 h-5 mr-3 text-gray-600 flex-shrink-0 mt-0.5;
}

.field-palette-label {
  @apply text-sm font-medium text-gray-700;
}
</style>
