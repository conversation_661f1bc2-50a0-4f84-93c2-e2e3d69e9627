<template>
  <div class="conditional-logic-builder">
    <div class="logic-header">
      <h4 class="logic-title">Conditional Logic</h4>
      <button @click="addCondition" class="add-condition-btn">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Add Condition
      </button>
    </div>

    <div v-if="conditions.length === 0" class="empty-conditions">
      <p class="empty-text">No conditions set. Click "Add Condition" to get started.</p>
    </div>

    <div v-else class="conditions-list">
      <!-- Logic Operator -->
      <div v-if="conditions.length > 1" class="logic-operator">
        <label class="operator-label">Show this field when:</label>
        <select v-model="logicOperator" @change="updateConditions" class="operator-select">
          <option value="AND">ALL conditions are met</option>
          <option value="OR">ANY condition is met</option>
        </select>
      </div>

      <!-- Conditions -->
      <div class="conditions">
        <div 
          v-for="(condition, index) in conditions" 
          :key="condition.id"
          class="condition-row"
        >
          <div class="condition-content">
            <!-- Field Selection -->
            <div class="condition-field">
              <label class="field-label">Field</label>
              <select 
                v-model="condition.field_id"
                @change="updateCondition(index)"
                class="field-select"
              >
                <option value="">Select field...</option>
                <option 
                  v-for="field in availableFields" 
                  :key="field.id"
                  :value="field.id"
                >
                  {{ field.label || field.name }}
                </option>
              </select>
            </div>

            <!-- Operator Selection -->
            <div class="condition-operator">
              <label class="field-label">Condition</label>
              <select 
                v-model="condition.operator"
                @change="updateCondition(index)"
                class="operator-select"
              >
                <option value="equals">equals</option>
                <option value="not_equals">does not equal</option>
                <option value="contains">contains</option>
                <option value="not_contains">does not contain</option>
                <option value="starts_with">starts with</option>
                <option value="ends_with">ends with</option>
                <option value="is_empty">is empty</option>
                <option value="is_not_empty">is not empty</option>
                <option value="greater_than">is greater than</option>
                <option value="less_than">is less than</option>
                <option value="greater_equal">is greater than or equal</option>
                <option value="less_equal">is less than or equal</option>
              </select>
            </div>

            <!-- Value Input -->
            <div v-if="!isEmptyOperator(condition.operator)" class="condition-value">
              <label class="field-label">Value</label>
              <component 
                :is="getValueInputComponent(condition)"
                v-model="condition.value"
                @input="updateCondition(index)"
                :field="getSelectedField(condition.field_id)"
                class="value-input"
              />
            </div>
          </div>

          <!-- Condition Actions -->
          <div class="condition-actions">
            <button 
              @click="duplicateCondition(index)"
              class="condition-action-btn"
              title="Duplicate condition"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
              </svg>
            </button>
            
            <button 
              @click="removeCondition(index)"
              class="condition-action-btn condition-action-delete"
              title="Remove condition"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>

          <!-- Logic Connector -->
          <div v-if="index < conditions.length - 1" class="logic-connector">
            <span class="connector-text">{{ logicOperator }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Options -->
    <div class="advanced-options">
      <button 
        @click="showAdvanced = !showAdvanced"
        class="advanced-toggle"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Advanced Options
      </button>

      <div v-if="showAdvanced" class="advanced-content">
        <div class="advanced-setting">
          <label class="setting-label">
            <input 
              type="checkbox" 
              v-model="settings.hideField"
              @change="updateSettings"
              class="setting-checkbox"
            />
            Hide field instead of showing
          </label>
        </div>

        <div class="advanced-setting">
          <label class="setting-label">
            <input 
              type="checkbox" 
              v-model="settings.clearValue"
              @change="updateSettings"
              class="setting-checkbox"
            />
            Clear field value when hidden
          </label>
        </div>

        <div class="advanced-setting">
          <label class="setting-label">Animation</label>
          <select v-model="settings.animation" @change="updateSettings" class="setting-select">
            <option value="none">None</option>
            <option value="fade">Fade</option>
            <option value="slide">Slide</option>
            <option value="scale">Scale</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div class="logic-preview">
      <h5 class="preview-title">Logic Summary</h5>
      <div class="preview-content">
        <p v-if="conditions.length === 0" class="preview-text">
          This field will always be visible.
        </p>
        <p v-else class="preview-text">
          {{ getLogicSummary() }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'

export default {
  name: 'ConditionalLogicBuilder',
  props: {
    conditions: {
      type: Array,
      default: () => []
    },
    availableFields: {
      type: Array,
      default: () => []
    },
    settings: {
      type: Object,
      default: () => ({
        hideField: false,
        clearValue: true,
        animation: 'fade'
      })
    }
  },
  emits: ['conditions-updated', 'settings-updated'],
  setup(props, { emit }) {
    const logicOperator = ref('AND')
    const showAdvanced = ref(false)
    const localConditions = ref([...props.conditions])
    const localSettings = ref({ ...props.settings })

    const addCondition = () => {
      const newCondition = {
        id: `condition_${Date.now()}`,
        field_id: '',
        operator: 'equals',
        value: ''
      }
      localConditions.value.push(newCondition)
      updateConditions()
    }

    const removeCondition = (index) => {
      localConditions.value.splice(index, 1)
      updateConditions()
    }

    const duplicateCondition = (index) => {
      const condition = localConditions.value[index]
      const duplicated = {
        ...condition,
        id: `condition_${Date.now()}`
      }
      localConditions.value.splice(index + 1, 0, duplicated)
      updateConditions()
    }

    const updateCondition = (index) => {
      updateConditions()
    }

    const updateConditions = () => {
      emit('conditions-updated', {
        operator: logicOperator.value,
        conditions: localConditions.value
      })
    }

    const updateSettings = () => {
      emit('settings-updated', localSettings.value)
    }

    const isEmptyOperator = (operator) => {
      return ['is_empty', 'is_not_empty'].includes(operator)
    }

    const getSelectedField = (fieldId) => {
      return props.availableFields.find(f => f.id === fieldId)
    }

    const getValueInputComponent = (condition) => {
      const field = getSelectedField(condition.field_id)
      if (!field) return 'input'

      switch (field.field_type) {
        case 'select':
        case 'radio':
          return 'select'
        case 'checkbox':
          return 'checkbox'
        case 'number':
        case 'integer':
        case 'decimal':
          return 'input'
        case 'date':
        case 'datetime':
          return 'input'
        default:
          return 'input'
      }
    }

    const getLogicSummary = () => {
      if (localConditions.value.length === 0) return ''

      const conditionTexts = localConditions.value.map(condition => {
        const field = getSelectedField(condition.field_id)
        const fieldName = field ? (field.label || field.name) : 'Unknown Field'
        const operator = getOperatorText(condition.operator)
        const value = condition.value || '(empty)'

        if (isEmptyOperator(condition.operator)) {
          return `"${fieldName}" ${operator}`
        }

        return `"${fieldName}" ${operator} "${value}"`
      })

      const connector = logicOperator.value === 'AND' ? ' AND ' : ' OR '
      const action = localSettings.value.hideField ? 'hide' : 'show'

      return `${action.charAt(0).toUpperCase() + action.slice(1)} this field when ${conditionTexts.join(connector)}`
    }

    const getOperatorText = (operator) => {
      const operators = {
        equals: 'equals',
        not_equals: 'does not equal',
        contains: 'contains',
        not_contains: 'does not contain',
        starts_with: 'starts with',
        ends_with: 'ends with',
        is_empty: 'is empty',
        is_not_empty: 'is not empty',
        greater_than: 'is greater than',
        less_than: 'is less than',
        greater_equal: 'is greater than or equal to',
        less_equal: 'is less than or equal to'
      }
      return operators[operator] || operator
    }

    // Watch for prop changes
    watch(() => props.conditions, (newConditions) => {
      localConditions.value = [...newConditions]
    }, { deep: true })

    watch(() => props.settings, (newSettings) => {
      localSettings.value = { ...newSettings }
    }, { deep: true })

    return {
      logicOperator,
      showAdvanced,
      localConditions,
      localSettings,
      addCondition,
      removeCondition,
      duplicateCondition,
      updateCondition,
      updateConditions,
      updateSettings,
      isEmptyOperator,
      getSelectedField,
      getValueInputComponent,
      getLogicSummary,
      conditions: localConditions,
      settings: localSettings
    }
  }
}
</script>

<style scoped>
.conditional-logic-builder {
  @apply space-y-4;
}

.logic-header {
  @apply flex items-center justify-between;
}

.logic-title {
  @apply text-sm font-medium text-gray-900;
}

.add-condition-btn {
  @apply flex items-center space-x-1 px-3 py-1 text-sm text-primary-600 border border-primary-300 rounded hover:bg-primary-50 transition-colors;
}

.empty-conditions {
  @apply p-4 text-center border border-dashed border-gray-300 rounded-lg;
}

.empty-text {
  @apply text-sm text-gray-500;
}

.logic-operator {
  @apply mb-4 p-3 bg-gray-50 rounded-lg;
}

.operator-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.operator-select {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.conditions {
  @apply space-y-4;
}

.condition-row {
  @apply relative p-4 border border-gray-200 rounded-lg;
}

.condition-content {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.condition-field,
.condition-operator,
.condition-value {
  @apply space-y-1;
}

.field-label {
  @apply block text-xs font-medium text-gray-700;
}

.field-select,
.value-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.condition-actions {
  @apply absolute top-2 right-2 flex items-center space-x-1;
}

.condition-action-btn {
  @apply p-1 text-gray-400 hover:text-gray-600 rounded transition-colors;
}

.condition-action-delete {
  @apply hover:text-red-600;
}

.logic-connector {
  @apply absolute -bottom-6 left-1/2 transform -translate-x-1/2 z-10;
}

.connector-text {
  @apply px-2 py-1 text-xs font-medium text-gray-600 bg-white border border-gray-300 rounded;
}

.advanced-options {
  @apply border-t border-gray-200 pt-4;
}

.advanced-toggle {
  @apply flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors;
}

.advanced-content {
  @apply mt-3 space-y-3;
}

.advanced-setting {
  @apply space-y-1;
}

.setting-label {
  @apply flex items-center space-x-2 text-sm text-gray-700;
}

.setting-checkbox {
  @apply form-checkbox text-primary-600;
}

.setting-select {
  @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.logic-preview {
  @apply p-3 bg-blue-50 rounded-lg;
}

.preview-title {
  @apply text-sm font-medium text-blue-900 mb-2;
}

.preview-content {
  @apply text-sm text-blue-800;
}

.preview-text {
  @apply leading-relaxed;
}
</style>
