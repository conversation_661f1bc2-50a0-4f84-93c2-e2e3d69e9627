/* Form Builder Main Styles */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  --form-primary-color: #3B82F6;
  --form-secondary-color: #6B7280;
  --form-background-color: #FFFFFF;
  --form-text-color: #1F2937;
  --form-border-color: #D1D5DB;
  --form-error-color: #EF4444;
  --form-success-color: #10B981;
  --form-font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --form-font-size: 16px;
  --form-line-height: 1.5;
  --form-font-weight: normal;
  --form-field-spacing: 16px;
  --form-padding: 24px;
  --form-section-spacing: 32px;
  --form-border-radius: 6px;
  --form-max-width: 600px;
  --form-animation-duration: 200ms;
  --form-animation-easing: ease-in-out;
}

/* Form Builder Layout */
.form-builder-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F9FAFB;
}

.builder-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: white;
  border-bottom: 1px solid #E5E7EB;
}

.builder-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.builder-sidebar {
  width: 20rem;
  background-color: white;
  border-right: 1px solid #E5E7EB;
  display: flex;
  flex-direction: column;
}

.builder-canvas {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.builder-properties {
  width: 20rem;
  background-color: white;
  border-left: 1px solid #E5E7EB;
  display: flex;
  flex-direction: column;
}

/* Sidebar Tabs */
.sidebar-tabs {
  display: flex;
  border-bottom: 1px solid #E5E7EB;
}

.sidebar-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s;
}

.sidebar-tab:hover {
  color: #374151;
  background-color: #F9FAFB;
}

.sidebar-tab.active {
  color: var(--form-primary-color);
  border-bottom: 2px solid var(--form-primary-color);
  background-color: #EFF6FF;
}

/* Field Palette */
.field-palette {
  padding: 1rem;
}

.field-palette-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  background-color: white;
}

.field-palette-item:hover {
  border-color: var(--form-primary-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.field-palette-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--form-primary-color);
}

.field-palette-label {
  font-weight: 500;
  color: #374151;
}

/* Form Canvas */
.canvas-container {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.form-canvas {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

/* Drop Zones */
.drop-zone {
  min-height: 60px;
  border: 2px dashed #D1D5DB;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0.5rem 0;
  transition: all 0.2s;
}

.drop-zone.active {
  border-color: var(--form-primary-color);
  background-color: #EFF6FF;
}

.drop-zone-text {
  color: #6B7280;
  font-size: 0.875rem;
  text-align: center;
}

/* Form Fields */
.form-field-wrapper {
  position: relative;
  padding: 1rem;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.form-field-wrapper:hover {
  border-color: #E5E7EB;
}

.form-field-wrapper.selected {
  border-color: var(--form-primary-color);
  background-color: #EFF6FF;
}

.field-controls {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.form-field-wrapper:hover .field-controls {
  opacity: 1;
}

.field-control-btn {
  padding: 0.25rem;
  background-color: white;
  border: 1px solid #E5E7EB;
  border-radius: 0.25rem;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.2s;
}

.field-control-btn:hover {
  color: #374151;
  border-color: #D1D5DB;
}

/* Form Inputs */
.form-container {
  max-width: var(--form-max-width);
  margin: 0 auto;
  padding: var(--form-padding);
  background-color: var(--form-background-color);
  color: var(--form-text-color);
  font-family: var(--form-font-family);
  font-size: var(--form-font-size);
  line-height: var(--form-line-height);
  font-weight: var(--form-font-weight);
}

.form-field {
  margin-bottom: var(--form-field-spacing);
  transition: all var(--form-animation-duration) var(--form-animation-easing);
}

.field-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--form-text-color);
}

.field-input,
.field-textarea,
.field-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--form-border-color);
  border-radius: var(--form-border-radius);
  background-color: var(--form-background-color);
  color: var(--form-text-color);
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  transition: all var(--form-animation-duration) var(--form-animation-easing);
}

.field-input:focus,
.field-textarea:focus,
.field-select:focus {
  outline: none;
  border-color: var(--form-primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.required-indicator {
  color: var(--form-error-color);
  margin-left: 0.25rem;
}

/* Customization Panel */
.customization-section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #E5E7EB;
}

.color-controls {
  display: grid;
  gap: 1rem;
}

.color-control {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.color-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.color-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.color-picker {
  width: 3rem;
  height: 2.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.375rem;
  cursor: pointer;
}

.color-text-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

/* Theme Grid */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
}

.theme-option {
  padding: 0.75rem;
  border: 2px solid #E5E7EB;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.theme-option:hover {
  border-color: #D1D5DB;
}

.theme-option.selected {
  border-color: var(--form-primary-color);
  background-color: #EFF6FF;
}

.theme-preview {
  height: 3rem;
  background-color: #F3F4F6;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem;
}

.theme-preview-field {
  height: 0.5rem;
  background-color: #E5E7EB;
  border-radius: 0.125rem;
}

.theme-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .builder-content {
    flex-direction: column;
  }
  
  .builder-sidebar,
  .builder-properties {
    width: 100%;
    height: auto;
    max-height: 300px;
  }
  
  .sidebar-tabs {
    overflow-x: auto;
  }
  
  .theme-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation Classes */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--form-animation-duration) var(--form-animation-easing);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform var(--form-animation-duration) var(--form-animation-easing);
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
