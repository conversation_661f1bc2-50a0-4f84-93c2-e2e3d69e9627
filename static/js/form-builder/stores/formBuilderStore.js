import { reactive, ref } from 'vue'

export function createFormBuilderStore() {
  // State
  const state = reactive({
    // UI State
    selectedField: null,
    showPreview: false,
    sidebarCollapsed: false,
    
    // Form State
    form: {
      id: null,
      name: '',
      description: '',
      slug: '',
      status: 'draft',
      settings: {}
    },
    
    fields: [],
    
    // Loading States
    loading: false,
    saving: false,
    
    // History for undo/redo
    history: [],
    historyIndex: -1,
    maxHistorySize: 50,
    
    // Validation
    errors: {},
    
    // Clipboard for copy/paste
    clipboard: null,

    // Bulk operations
    selectedFields: [],

    // Keyboard shortcuts
    keyboardShortcuts: {
      'ctrl+z': 'undo',
      'ctrl+y': 'redo',
      'ctrl+c': 'copy',
      'ctrl+v': 'paste',
      'ctrl+d': 'duplicate',
      'delete': 'delete',
      'ctrl+a': 'selectAll'
    }
  })
  
  // Actions
  const actions = {
    // Field Selection
    selectField(field) {
      state.selectedField = field
    },
    
    clearSelection() {
      state.selectedField = null
    },
    
    // Preview Toggle
    togglePreview() {
      state.showPreview = !state.showPreview
      if (state.showPreview) {
        state.selectedField = null
      }
    },
    
    // Sidebar Toggle
    toggleSidebar() {
      state.sidebarCollapsed = !state.sidebarCollapsed
    },
    
    // Form Management
    setForm(formData) {
      Object.assign(state.form, formData)
    },
    
    updateForm(updates) {
      Object.assign(state.form, updates)
      this.saveToHistory('form_updated')
    },
    
    // Field Management
    setFields(fields) {
      state.fields = [...fields]
    },
    
    addField(field) {
      state.fields.push(field)
      this.saveToHistory('field_added')
    },
    
    updateField(fieldData) {
      const index = state.fields.findIndex(f => f.id === fieldData.id)
      if (index !== -1) {
        state.fields[index] = { ...state.fields[index], ...fieldData }
        this.saveToHistory('field_updated')
      }
    },
    
    deleteField(fieldId) {
      const index = state.fields.findIndex(f => f.id === fieldId)
      if (index !== -1) {
        state.fields.splice(index, 1)
        if (state.selectedField && state.selectedField.id === fieldId) {
          state.selectedField = null
        }
        this.saveToHistory('field_deleted')
      }
    },
    
    duplicateField(field) {
      const duplicatedField = {
        ...field,
        id: `field_${Date.now()}`,
        name: `${field.name}_copy`,
        label: `${field.label} (Copy)`
      }
      
      const originalIndex = state.fields.findIndex(f => f.id === field.id)
      state.fields.splice(originalIndex + 1, 0, duplicatedField)
      this.saveToHistory('field_duplicated')
      
      return duplicatedField
    },
    
    reorderFields(newFields) {
      state.fields = newFields.map((field, index) => ({
        ...field,
        order: index + 1
      }))
      this.saveToHistory('fields_reordered')
    },
    
    // Clipboard Operations
    copyField(field) {
      state.clipboard = {
        type: 'field',
        data: { ...field }
      }
    },
    
    pasteField() {
      if (state.clipboard && state.clipboard.type === 'field') {
        const pastedField = {
          ...state.clipboard.data,
          id: `field_${Date.now()}`,
          name: `${state.clipboard.data.name}_paste`,
          label: `${state.clipboard.data.label} (Pasted)`
        }
        
        this.addField(pastedField)
        return pastedField
      }
    },
    
    // History Management
    saveToHistory(action, description = '') {
      const snapshot = {
        action,
        description,
        timestamp: Date.now(),
        form: { ...state.form },
        fields: state.fields.map(f => ({ ...f, properties: { ...f.properties } }))
      }

      // Remove any history after current index (for redo functionality)
      state.history = state.history.slice(0, state.historyIndex + 1)

      // Add new snapshot
      state.history.push(snapshot)
      state.historyIndex = state.history.length - 1

      // Limit history size
      if (state.history.length > state.maxHistorySize) {
        state.history.shift()
        state.historyIndex--
      }

      console.log(`History: ${action} - ${description}`, snapshot)
    },
    
    undo() {
      if (this.canUndo()) {
        state.historyIndex--
        const snapshot = state.history[state.historyIndex]
        this.restoreSnapshot(snapshot)
      }
    },
    
    redo() {
      if (this.canRedo()) {
        state.historyIndex++
        const snapshot = state.history[state.historyIndex]
        this.restoreSnapshot(snapshot)
      }
    },
    
    canUndo() {
      return state.historyIndex > 0
    },
    
    canRedo() {
      return state.historyIndex < state.history.length - 1
    },
    
    restoreSnapshot(snapshot) {
      Object.assign(state.form, snapshot.form)
      state.fields = snapshot.fields.map(f => ({ ...f }))
      state.selectedField = null
    },
    
    // Validation
    setErrors(errors) {
      state.errors = { ...errors }
    },
    
    clearErrors() {
      state.errors = {}
    },
    
    addError(field, message) {
      state.errors[field] = message
    },
    
    removeError(field) {
      delete state.errors[field]
    },
    
    // Loading States
    setLoading(loading) {
      state.loading = loading
    },
    
    setSaving(saving) {
      state.saving = saving
    },
    
    // Utility Methods
    getFieldById(fieldId) {
      return state.fields.find(f => f.id === fieldId)
    },
    
    getFieldByName(fieldName) {
      return state.fields.find(f => f.name === fieldName)
    },
    
    getFieldIndex(fieldId) {
      return state.fields.findIndex(f => f.id === fieldId)
    },
    
    getNextFieldOrder() {
      return Math.max(...state.fields.map(f => f.order || 0), 0) + 1
    },
    
    validateFieldName(name, excludeId = null) {
      return !state.fields.some(f => f.name === name && f.id !== excludeId)
    },
    
    generateUniqueFieldName(baseName) {
      let counter = 1
      let name = baseName

      while (!this.validateFieldName(name)) {
        name = `${baseName}_${counter}`
        counter++
      }

      return name
    },

    // Clipboard Management
    copyToClipboard(field) {
      state.clipboard = { ...field }
    },

    pasteFromClipboard() {
      if (state.clipboard) {
        const newField = {
          ...state.clipboard,
          id: `field_${Date.now()}`,
          name: `${state.clipboard.name}_copy`,
          label: `${state.clipboard.label} (Copy)`
        }

        this.addField(newField)
        return newField
      }
      return null
    },

    // Bulk Operations
    selectField(fieldId) {
      if (!state.selectedFields.includes(fieldId)) {
        state.selectedFields.push(fieldId)
      }
    },

    deselectField(fieldId) {
      const index = state.selectedFields.indexOf(fieldId)
      if (index > -1) {
        state.selectedFields.splice(index, 1)
      }
    },

    selectAllFields() {
      state.selectedFields = state.fields.map(f => f.id)
    },

    clearSelection() {
      state.selectedFields = []
    },

    deleteSelectedFields() {
      const fieldsToDelete = [...state.selectedFields]
      state.fields = state.fields.filter(f => !fieldsToDelete.includes(f.id))
      state.selectedFields = []
      this.saveToHistory('bulk_delete', `Deleted ${fieldsToDelete.length} fields`)
    },

    duplicateSelectedFields() {
      const fieldsToDuplicate = state.fields.filter(f => state.selectedFields.includes(f.id))
      const newFields = []

      fieldsToDuplicate.forEach(field => {
        const newField = {
          ...field,
          id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: `${field.name}_copy`,
          label: `${field.label} (Copy)`,
          order: state.fields.length + newFields.length + 1
        }
        newFields.push(newField)
      })

      state.fields.push(...newFields)
      state.selectedFields = newFields.map(f => f.id)
      this.saveToHistory('bulk_duplicate', `Duplicated ${fieldsToDuplicate.length} fields`)
    }
  }
  
  // Getters
  const getters = {
    visibleFields() {
      return state.fields.filter(f => f.is_visible).sort((a, b) => a.order - b.order)
    },
    
    requiredFields() {
      return state.fields.filter(f => f.required)
    },
    
    hasUnsavedChanges() {
      return state.historyIndex > 0
    },
    
    fieldCount() {
      return state.fields.length
    },
    
    hasErrors() {
      return Object.keys(state.errors).length > 0
    }
  }
  
  return {
    state,
    actions,
    getters
  }
}
