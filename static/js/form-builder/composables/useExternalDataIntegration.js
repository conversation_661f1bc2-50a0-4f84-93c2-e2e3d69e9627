import { ref, reactive, computed } from 'vue'

export function useExternalDataIntegration() {
  const integrations = ref([])
  const activeConnections = reactive(new Map())
  const dataCache = reactive(new Map())
  const isLoading = ref(false)
  const errors = ref([])

  // Supported integration types
  const integrationTypes = {
    REST_API: {
      name: 'REST API',
      description: 'Connect to any REST API endpoint',
      fields: ['url', 'method', 'headers', 'authentication'],
      icon: 'api'
    },
    GOOGLE_SHEETS: {
      name: 'Google Sheets',
      description: 'Connect to Google Sheets for data lookup',
      fields: ['spreadsheetId', 'range', 'apiKey'],
      icon: 'google-sheets'
    },
    AIRTABLE: {
      name: 'Airtable',
      description: 'Connect to Airtable bases',
      fields: ['baseId', 'tableId', 'apiKey'],
      icon: 'airtable'
    },
    SALESFORCE: {
      name: 'Salesforce',
      description: 'Connect to Salesforce CRM',
      fields: ['instanceUrl', 'clientId', 'clientSecret', 'username', 'password'],
      icon: 'salesforce'
    },
    HUBSPOT: {
      name: 'HubSpot',
      description: 'Connect to HubSpot CRM',
      fields: ['apiKey', 'portalId'],
      icon: 'hubspot'
    },
    MAILCHIMP: {
      name: 'Mailchimp',
      description: 'Connect to Mailchimp for email marketing',
      fields: ['apiKey', 'listId'],
      icon: 'mailchimp'
    },
    WEBHOOK: {
      name: 'Webhook',
      description: 'Send data to webhook endpoints',
      fields: ['url', 'method', 'headers', 'authentication'],
      icon: 'webhook'
    },
    DATABASE: {
      name: 'Database',
      description: 'Connect to external databases',
      fields: ['connectionString', 'query', 'authentication'],
      icon: 'database'
    }
  }

  // Authentication types
  const authTypes = {
    NONE: { name: 'None', fields: [] },
    API_KEY: { name: 'API Key', fields: ['apiKey', 'keyLocation'] },
    BEARER_TOKEN: { name: 'Bearer Token', fields: ['token'] },
    BASIC_AUTH: { name: 'Basic Auth', fields: ['username', 'password'] },
    OAUTH2: { name: 'OAuth 2.0', fields: ['clientId', 'clientSecret', 'scope'] }
  }

  // Create a new integration
  const createIntegration = (config) => {
    const integration = {
      id: `integration-${Date.now()}`,
      name: config.name,
      type: config.type,
      description: config.description,
      config: config.config,
      authentication: config.authentication,
      mapping: config.mapping || {},
      isActive: false,
      lastSync: null,
      status: 'disconnected',
      createdAt: new Date().toISOString(),
      ...config
    }

    integrations.value.push(integration)
    return integration
  }

  // Test integration connection
  const testConnection = async (integration) => {
    isLoading.value = true
    errors.value = []

    try {
      const result = await performConnectionTest(integration)
      integration.status = result.success ? 'connected' : 'error'
      integration.lastTest = new Date().toISOString()
      
      if (!result.success) {
        errors.value.push({
          integrationId: integration.id,
          message: result.error,
          timestamp: new Date().toISOString()
        })
      }

      return result
    } catch (error) {
      integration.status = 'error'
      errors.value.push({
        integrationId: integration.id,
        message: error.message,
        timestamp: new Date().toISOString()
      })
      return { success: false, error: error.message }
    } finally {
      isLoading.value = false
    }
  }

  // Perform actual connection test based on integration type
  const performConnectionTest = async (integration) => {
    switch (integration.type) {
      case 'REST_API':
        return await testRestApiConnection(integration)
      case 'GOOGLE_SHEETS':
        return await testGoogleSheetsConnection(integration)
      case 'AIRTABLE':
        return await testAirtableConnection(integration)
      case 'SALESFORCE':
        return await testSalesforceConnection(integration)
      case 'HUBSPOT':
        return await testHubSpotConnection(integration)
      case 'MAILCHIMP':
        return await testMailchimpConnection(integration)
      case 'WEBHOOK':
        return await testWebhookConnection(integration)
      case 'DATABASE':
        return await testDatabaseConnection(integration)
      default:
        throw new Error(`Unsupported integration type: ${integration.type}`)
    }
  }

  // REST API connection test
  const testRestApiConnection = async (integration) => {
    const { url, method = 'GET', headers = {}, authentication } = integration.config

    const requestHeaders = { ...headers }
    
    // Add authentication headers
    if (authentication) {
      switch (authentication.type) {
        case 'API_KEY':
          if (authentication.keyLocation === 'header') {
            requestHeaders[authentication.keyName || 'X-API-Key'] = authentication.apiKey
          }
          break
        case 'BEARER_TOKEN':
          requestHeaders['Authorization'] = `Bearer ${authentication.token}`
          break
        case 'BASIC_AUTH':
          const credentials = btoa(`${authentication.username}:${authentication.password}`)
          requestHeaders['Authorization'] = `Basic ${credentials}`
          break
      }
    }

    const response = await fetch(url, {
      method,
      headers: requestHeaders
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return { success: true, data }
  }

  // Google Sheets connection test
  const testGoogleSheetsConnection = async (integration) => {
    const { spreadsheetId, range, apiKey } = integration.config
    const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}?key=${apiKey}`

    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.statusText}`)
    }

    const data = await response.json()
    return { success: true, data: data.values }
  }

  // Airtable connection test
  const testAirtableConnection = async (integration) => {
    const { baseId, tableId, apiKey } = integration.config
    const url = `https://api.airtable.com/v0/${baseId}/${tableId}?maxRecords=1`

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    })

    if (!response.ok) {
      throw new Error(`Airtable API error: ${response.statusText}`)
    }

    const data = await response.json()
    return { success: true, data: data.records }
  }

  // Salesforce connection test
  const testSalesforceConnection = async (integration) => {
    // This would typically involve OAuth flow
    // For now, return a mock success
    return { success: true, data: { message: 'Salesforce connection test successful' } }
  }

  // HubSpot connection test
  const testHubSpotConnection = async (integration) => {
    const { apiKey } = integration.config
    const url = `https://api.hubapi.com/contacts/v1/lists/all/contacts/all?hapikey=${apiKey}&count=1`

    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HubSpot API error: ${response.statusText}`)
    }

    const data = await response.json()
    return { success: true, data }
  }

  // Mailchimp connection test
  const testMailchimpConnection = async (integration) => {
    const { apiKey, listId } = integration.config
    const datacenter = apiKey.split('-')[1]
    const url = `https://${datacenter}.api.mailchimp.com/3.0/lists/${listId}`

    const response = await fetch(url, {
      headers: {
        'Authorization': `apikey ${apiKey}`
      }
    })

    if (!response.ok) {
      throw new Error(`Mailchimp API error: ${response.statusText}`)
    }

    const data = await response.json()
    return { success: true, data }
  }

  // Webhook connection test
  const testWebhookConnection = async (integration) => {
    const { url, method = 'POST', headers = {} } = integration.config

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify({ test: true, timestamp: new Date().toISOString() })
    })

    if (!response.ok) {
      throw new Error(`Webhook test failed: ${response.statusText}`)
    }

    return { success: true, data: { message: 'Webhook test successful' } }
  }

  // Database connection test
  const testDatabaseConnection = async (integration) => {
    // This would typically involve a backend API call
    // For now, return a mock success
    return { success: true, data: { message: 'Database connection test successful' } }
  }

  // Fetch data from integration
  const fetchData = async (integrationId, params = {}) => {
    const integration = integrations.value.find(i => i.id === integrationId)
    if (!integration) {
      throw new Error('Integration not found')
    }

    const cacheKey = `${integrationId}-${JSON.stringify(params)}`
    
    // Check cache first
    if (dataCache.has(cacheKey)) {
      const cached = dataCache.get(cacheKey)
      if (Date.now() - cached.timestamp < 300000) { // 5 minutes cache
        return cached.data
      }
    }

    isLoading.value = true

    try {
      const data = await performDataFetch(integration, params)
      
      // Cache the result
      dataCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })

      integration.lastSync = new Date().toISOString()
      return data
    } catch (error) {
      errors.value.push({
        integrationId,
        message: error.message,
        timestamp: new Date().toISOString()
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Perform data fetch based on integration type
  const performDataFetch = async (integration, params) => {
    switch (integration.type) {
      case 'REST_API':
        return await fetchRestApiData(integration, params)
      case 'GOOGLE_SHEETS':
        return await fetchGoogleSheetsData(integration, params)
      case 'AIRTABLE':
        return await fetchAirtableData(integration, params)
      default:
        throw new Error(`Data fetching not implemented for ${integration.type}`)
    }
  }

  // REST API data fetch
  const fetchRestApiData = async (integration, params) => {
    const { url, method = 'GET', headers = {} } = integration.config
    
    let requestUrl = url
    if (method === 'GET' && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams(params)
      requestUrl += `?${searchParams.toString()}`
    }

    const response = await fetch(requestUrl, {
      method,
      headers,
      body: method !== 'GET' ? JSON.stringify(params) : undefined
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`)
    }

    return await response.json()
  }

  // Google Sheets data fetch
  const fetchGoogleSheetsData = async (integration, params) => {
    const { spreadsheetId, range, apiKey } = integration.config
    const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}?key=${apiKey}`

    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.statusText}`)
    }

    const data = await response.json()
    return data.values || []
  }

  // Airtable data fetch
  const fetchAirtableData = async (integration, params) => {
    const { baseId, tableId, apiKey } = integration.config
    let url = `https://api.airtable.com/v0/${baseId}/${tableId}`

    if (params.filterByFormula) {
      url += `?filterByFormula=${encodeURIComponent(params.filterByFormula)}`
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    })

    if (!response.ok) {
      throw new Error(`Airtable API error: ${response.statusText}`)
    }

    const data = await response.json()
    return data.records || []
  }

  // Send data to integration
  const sendData = async (integrationId, data) => {
    const integration = integrations.value.find(i => i.id === integrationId)
    if (!integration) {
      throw new Error('Integration not found')
    }

    isLoading.value = true

    try {
      const result = await performDataSend(integration, data)
      integration.lastSync = new Date().toISOString()
      return result
    } catch (error) {
      errors.value.push({
        integrationId,
        message: error.message,
        timestamp: new Date().toISOString()
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Perform data send based on integration type
  const performDataSend = async (integration, data) => {
    switch (integration.type) {
      case 'WEBHOOK':
        return await sendWebhookData(integration, data)
      case 'AIRTABLE':
        return await sendAirtableData(integration, data)
      case 'MAILCHIMP':
        return await sendMailchimpData(integration, data)
      default:
        throw new Error(`Data sending not implemented for ${integration.type}`)
    }
  }

  // Webhook data send
  const sendWebhookData = async (integration, data) => {
    const { url, method = 'POST', headers = {} } = integration.config

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error(`Webhook failed: ${response.statusText}`)
    }

    return await response.json()
  }

  // Airtable data send
  const sendAirtableData = async (integration, data) => {
    const { baseId, tableId, apiKey } = integration.config
    const url = `https://api.airtable.com/v0/${baseId}/${tableId}`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        fields: data
      })
    })

    if (!response.ok) {
      throw new Error(`Airtable create failed: ${response.statusText}`)
    }

    return await response.json()
  }

  // Mailchimp data send
  const sendMailchimpData = async (integration, data) => {
    const { apiKey, listId } = integration.config
    const datacenter = apiKey.split('-')[1]
    const url = `https://${datacenter}.api.mailchimp.com/3.0/lists/${listId}/members`

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `apikey ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error(`Mailchimp subscription failed: ${response.statusText}`)
    }

    return await response.json()
  }

  // Get available integrations
  const getAvailableIntegrations = () => {
    return Object.entries(integrationTypes).map(([key, value]) => ({
      type: key,
      ...value
    }))
  }

  // Get integration status
  const getIntegrationStatus = (integrationId) => {
    const integration = integrations.value.find(i => i.id === integrationId)
    return integration ? integration.status : 'not_found'
  }

  // Clear cache
  const clearCache = (integrationId = null) => {
    if (integrationId) {
      for (const [key] of dataCache) {
        if (key.startsWith(integrationId)) {
          dataCache.delete(key)
        }
      }
    } else {
      dataCache.clear()
    }
  }

  // Remove integration
  const removeIntegration = (integrationId) => {
    const index = integrations.value.findIndex(i => i.id === integrationId)
    if (index > -1) {
      integrations.value.splice(index, 1)
      clearCache(integrationId)
      activeConnections.delete(integrationId)
    }
  }

  return {
    integrations,
    integrationTypes,
    authTypes,
    isLoading,
    errors,
    createIntegration,
    testConnection,
    fetchData,
    sendData,
    getAvailableIntegrations,
    getIntegrationStatus,
    clearCache,
    removeIntegration
  }
}
