import { ref, reactive, computed, watch, onBeforeUnmount } from 'vue'

export function useAutoSave(formData, options = {}) {
  const {
    interval = 30000, // 30 seconds
    maxVersions = 10,
    storageKey = 'formBuilder_autoSave',
    enableCloudSync = false,
    conflictResolution = 'prompt' // 'prompt', 'local', 'remote'
  } = options
  
  const isAutoSaveEnabled = ref(true)
  const isSaving = ref(false)
  const lastSaved = ref(null)
  const saveStatus = ref('saved') // 'saved', 'saving', 'error', 'unsaved'
  const saveError = ref(null)
  const hasUnsavedChanges = ref(false)
  
  const autoSaveHistory = ref([])
  const recoveryData = ref(null)
  const conflictData = ref(null)
  const showRecoveryPrompt = ref(false)
  const showConflictDialog = ref(false)
  
  let autoSaveTimer = null
  let changeDetectionTimer = null
  let lastFormSnapshot = null
  
  // Auto-save statistics
  const saveStats = reactive({
    totalSaves: 0,
    successfulSaves: 0,
    failedSaves: 0,
    averageSaveTime: 0,
    lastSaveTime: 0
  })
  
  // Computed properties
  const timeSinceLastSave = computed(() => {
    if (!lastSaved.value) return null
    return Date.now() - lastSaved.value
  })
  
  const saveStatusMessage = computed(() => {
    switch (saveStatus.value) {
      case 'saved':
        return lastSaved.value ? `Saved ${formatTimeAgo(lastSaved.value)}` : 'Saved'
      case 'saving':
        return 'Saving...'
      case 'error':
        return `Save failed: ${saveError.value}`
      case 'unsaved':
        return 'Unsaved changes'
      default:
        return ''
    }
  })
  
  const canRecover = computed(() => {
    return recoveryData.value && recoveryData.value.timestamp > (lastSaved.value || 0)
  })
  
  // Initialize auto-save system
  const initializeAutoSave = () => {
    // Check for existing auto-save data
    checkForRecoveryData()
    
    // Set up change detection
    setupChangeDetection()
    
    // Start auto-save timer
    if (isAutoSaveEnabled.value) {
      startAutoSave()
    }
    
    // Set up beforeunload handler
    setupBeforeUnloadHandler()
    
    // Set up visibility change handler
    setupVisibilityChangeHandler()
    
    // Initial snapshot
    lastFormSnapshot = createFormSnapshot(formData.value)
  }
  
  // Create a snapshot of current form data
  const createFormSnapshot = (data) => {
    return {
      timestamp: Date.now(),
      data: JSON.parse(JSON.stringify(data)),
      checksum: generateChecksum(data)
    }
  }
  
  // Generate checksum for data integrity
  const generateChecksum = (data) => {
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }
  
  // Detect changes in form data
  const setupChangeDetection = () => {
    watch(formData, (newData) => {
      if (changeDetectionTimer) {
        clearTimeout(changeDetectionTimer)
      }
      
      // Debounce change detection
      changeDetectionTimer = setTimeout(() => {
        const newSnapshot = createFormSnapshot(newData)
        
        if (lastFormSnapshot && newSnapshot.checksum !== lastFormSnapshot.checksum) {
          hasUnsavedChanges.value = true
          saveStatus.value = 'unsaved'
          
          // Update snapshot
          lastFormSnapshot = newSnapshot
        }
      }, 1000) // 1 second debounce
    }, { deep: true })
  }
  
  // Start auto-save timer
  const startAutoSave = () => {
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
    }
    
    autoSaveTimer = setInterval(() => {
      if (hasUnsavedChanges.value && !isSaving.value) {
        performAutoSave()
      }
    }, interval)
  }
  
  // Stop auto-save timer
  const stopAutoSave = () => {
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
    }
  }
  
  // Perform auto-save
  const performAutoSave = async () => {
    if (isSaving.value || !hasUnsavedChanges.value) return
    
    const saveStartTime = Date.now()
    isSaving.value = true
    saveStatus.value = 'saving'
    saveError.value = null
    
    try {
      const snapshot = createFormSnapshot(formData.value)
      
      // Save to local storage
      await saveToLocalStorage(snapshot)
      
      // Save to cloud if enabled
      if (enableCloudSync) {
        await saveToCloud(snapshot)
      }
      
      // Update save history
      addToSaveHistory(snapshot)
      
      // Update status
      lastSaved.value = Date.now()
      hasUnsavedChanges.value = false
      saveStatus.value = 'saved'
      
      // Update statistics
      const saveTime = Date.now() - saveStartTime
      updateSaveStats(true, saveTime)
      
    } catch (error) {
      console.error('Auto-save failed:', error)
      saveError.value = error.message
      saveStatus.value = 'error'
      updateSaveStats(false)
      
      // Retry after delay
      setTimeout(() => {
        if (hasUnsavedChanges.value) {
          performAutoSave()
        }
      }, 5000)
    } finally {
      isSaving.value = false
    }
  }
  
  // Save to local storage
  const saveToLocalStorage = async (snapshot) => {
    const saveData = {
      ...snapshot,
      version: '1.0',
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    try {
      localStorage.setItem(storageKey, JSON.stringify(saveData))
    } catch (error) {
      if (error.name === 'QuotaExceededError') {
        // Clean up old saves and retry
        cleanupOldSaves()
        localStorage.setItem(storageKey, JSON.stringify(saveData))
      } else {
        throw error
      }
    }
  }
  
  // Save to cloud (mock implementation)
  const saveToCloud = async (snapshot) => {
    // This would integrate with your backend API
    const response = await fetch('/api/forms/autosave', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value
      },
      body: JSON.stringify({
        formId: formData.value.id,
        data: snapshot.data,
        timestamp: snapshot.timestamp,
        checksum: snapshot.checksum
      })
    })
    
    if (!response.ok) {
      throw new Error(`Cloud save failed: ${response.statusText}`)
    }
    
    const result = await response.json()
    
    // Check for conflicts
    if (result.conflict) {
      handleSaveConflict(result.conflictData, snapshot)
    }
    
    return result
  }
  
  // Handle save conflicts
  const handleSaveConflict = (remoteData, localData) => {
    conflictData.value = {
      remote: remoteData,
      local: localData,
      timestamp: Date.now()
    }
    
    switch (conflictResolution) {
      case 'local':
        // Keep local changes, overwrite remote
        break
      case 'remote':
        // Use remote data, discard local changes
        Object.assign(formData.value, remoteData.data)
        hasUnsavedChanges.value = false
        break
      case 'prompt':
      default:
        // Show conflict resolution dialog
        showConflictDialog.value = true
        break
    }
  }
  
  // Add to save history
  const addToSaveHistory = (snapshot) => {
    autoSaveHistory.value.unshift({
      id: `save_${Date.now()}`,
      timestamp: snapshot.timestamp,
      checksum: snapshot.checksum,
      size: JSON.stringify(snapshot.data).length,
      type: 'auto'
    })
    
    // Keep only recent saves
    if (autoSaveHistory.value.length > maxVersions) {
      autoSaveHistory.value = autoSaveHistory.value.slice(0, maxVersions)
    }
  }
  
  // Check for recovery data on initialization
  const checkForRecoveryData = () => {
    try {
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        const data = JSON.parse(saved)
        
        // Check if recovery data is newer than current form
        const currentTimestamp = formData.value.updatedAt ? 
          new Date(formData.value.updatedAt).getTime() : 0
        
        if (data.timestamp > currentTimestamp) {
          recoveryData.value = data
          showRecoveryPrompt.value = true
        }
      }
    } catch (error) {
      console.warn('Failed to check recovery data:', error)
    }
  }
  
  // Recover from auto-save
  const recoverFromAutoSave = () => {
    if (recoveryData.value) {
      Object.assign(formData.value, recoveryData.value.data)
      lastSaved.value = recoveryData.value.timestamp
      hasUnsavedChanges.value = false
      saveStatus.value = 'saved'
      
      showRecoveryPrompt.value = false
      recoveryData.value = null
      
      // Update snapshot
      lastFormSnapshot = createFormSnapshot(formData.value)
    }
  }
  
  // Dismiss recovery prompt
  const dismissRecovery = () => {
    showRecoveryPrompt.value = false
    recoveryData.value = null
    
    // Clear auto-save data
    localStorage.removeItem(storageKey)
  }
  
  // Manual save
  const manualSave = async () => {
    await performAutoSave()
    
    // Add to history as manual save
    if (autoSaveHistory.value.length > 0) {
      autoSaveHistory.value[0].type = 'manual'
    }
  }
  
  // Load specific version
  const loadVersion = (versionId) => {
    const version = autoSaveHistory.value.find(v => v.id === versionId)
    if (version) {
      // This would load the actual data for the version
      console.log('Loading version:', versionId)
    }
  }
  
  // Clean up old saves
  const cleanupOldSaves = () => {
    // Remove old auto-save data to free up storage
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('formBuilder_autoSave_old_')) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
  }
  
  // Update save statistics
  const updateSaveStats = (success, saveTime = 0) => {
    saveStats.totalSaves++
    
    if (success) {
      saveStats.successfulSaves++
      saveStats.lastSaveTime = saveTime
      saveStats.averageSaveTime = 
        (saveStats.averageSaveTime + saveTime) / saveStats.successfulSaves
    } else {
      saveStats.failedSaves++
    }
  }
  
  // Setup beforeunload handler
  const setupBeforeUnloadHandler = () => {
    window.addEventListener('beforeunload', (event) => {
      if (hasUnsavedChanges.value) {
        // Perform synchronous save
        try {
          const snapshot = createFormSnapshot(formData.value)
          localStorage.setItem(`${storageKey}_emergency`, JSON.stringify(snapshot))
        } catch (error) {
          console.error('Emergency save failed:', error)
        }
        
        // Show confirmation dialog
        event.preventDefault()
        event.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
        return event.returnValue
      }
    })
  }
  
  // Setup visibility change handler
  const setupVisibilityChangeHandler = () => {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && hasUnsavedChanges.value) {
        // Save when tab becomes hidden
        performAutoSave()
      }
    })
  }
  
  // Format time ago
  const formatTimeAgo = (timestamp) => {
    const diff = Date.now() - timestamp
    const minutes = Math.floor(diff / 60000)
    const seconds = Math.floor((diff % 60000) / 1000)
    
    if (minutes > 0) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`
    } else {
      return `${seconds} second${seconds !== 1 ? 's' : ''} ago`
    }
  }
  
  // Toggle auto-save
  const toggleAutoSave = () => {
    isAutoSaveEnabled.value = !isAutoSaveEnabled.value
    
    if (isAutoSaveEnabled.value) {
      startAutoSave()
    } else {
      stopAutoSave()
    }
  }
  
  // Cleanup on unmount
  onBeforeUnmount(() => {
    stopAutoSave()
    
    if (changeDetectionTimer) {
      clearTimeout(changeDetectionTimer)
    }
    
    // Final save if needed
    if (hasUnsavedChanges.value) {
      performAutoSave()
    }
  })
  
  return {
    isAutoSaveEnabled,
    isSaving,
    lastSaved,
    saveStatus,
    saveError,
    hasUnsavedChanges,
    autoSaveHistory,
    recoveryData,
    showRecoveryPrompt,
    showConflictDialog,
    conflictData,
    saveStats,
    timeSinceLastSave,
    saveStatusMessage,
    canRecover,
    initializeAutoSave,
    performAutoSave,
    manualSave,
    recoverFromAutoSave,
    dismissRecovery,
    loadVersion,
    toggleAutoSave
  }
}
