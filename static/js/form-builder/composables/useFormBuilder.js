import { ref, reactive } from 'vue'
import { axios } from '../../main.js'

export function useFormBuilder(formDataOrSlug, apiBaseUrl) {
  const form = reactive({
    id: null,
    name: '',
    description: '',
    slug: '',
    status: 'draft',
    settings: {}
  })

  const fields = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Check if we received form data directly or a slug
  const isFormData = typeof formDataOrSlug === 'object' && formDataOrSlug !== null

  if (isFormData) {
    // Initialize with provided form data
    Object.assign(form, formDataOrSlug)
    fields.value = formDataOrSlug.fields || []
    console.log('Form builder initialized with data:', form)
  }
  
  // Load form data
  const loadForm = async () => {
    // If we already have form data, don't load from API
    if (isFormData) {
      console.log('Form data already provided, skipping API load')
      return
    }

    const formSlug = formDataOrSlug
    if (!formSlug) return

    loading.value = true
    error.value = null

    try {
      const response = await axios.get(`${apiBaseUrl}/forms/${formSlug}/`)

      Object.assign(form, response.data)
      fields.value = response.data.fields || []

    } catch (err) {
      error.value = err.response?.data?.detail || 'Failed to load form'
      console.error('Error loading form:', err)
    } finally {
      loading.value = false
    }
  }
  
  // Save form data
  const saveForm = async () => {
    if (!form.id) return
    
    loading.value = true
    error.value = null
    
    try {
      // Save form basic info
      const formResponse = await axios.patch(`${apiBaseUrl}/forms/${form.slug}/`, {
        name: form.name,
        description: form.description,
        settings: form.settings
      })
      
      Object.assign(form, formResponse.data)
      
      // Save fields
      await saveFields()
      
    } catch (err) {
      error.value = err.response?.data?.detail || 'Failed to save form'
      console.error('Error saving form:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // Save all fields
  const saveFields = async () => {
    const promises = fields.value.map(field => {
      if (field.id && field.id.toString().startsWith('field_')) {
        // New field - create
        return createField(field)
      } else {
        // Existing field - update
        return updateFieldOnServer(field)
      }
    })
    
    await Promise.all(promises)
  }
  
  // Create new field on server
  const createField = async (field) => {
    try {
      const response = await axios.post(`${apiBaseUrl}/fields/`, {
        form: form.id,
        ...field
      })
      
      // Update local field with server response
      const index = fields.value.findIndex(f => f.id === field.id)
      if (index !== -1) {
        fields.value[index] = response.data
      }
      
      return response.data
    } catch (err) {
      console.error('Error creating field:', err)
      throw err
    }
  }
  
  // Update existing field on server
  const updateFieldOnServer = async (field) => {
    try {
      const response = await axios.patch(`${apiBaseUrl}/fields/${field.id}/`, field)
      return response.data
    } catch (err) {
      console.error('Error updating field:', err)
      throw err
    }
  }
  
  // Add new field
  const addField = (fieldType) => {
    const fieldId = `field_${Date.now()}`
    const fieldCount = fields.value.length + 1
    
    const newField = {
      id: fieldId,
      field_type: fieldType,
      name: `field_${fieldCount}`,
      label: getDefaultLabel(fieldType),
      placeholder: '',
      help_text: '',
      required: false,
      order: fieldCount,
      width: 'full',
      is_visible: true,
      properties: getDefaultProperties(fieldType),
      conditional_logic: {}
    }
    
    fields.value.push(newField)
    return newField
  }
  
  // Update field
  const updateField = (fieldData) => {
    const index = fields.value.findIndex(f => f.id === fieldData.id)
    if (index !== -1) {
      fields.value[index] = { ...fields.value[index], ...fieldData }
    }
  }
  
  // Delete field
  const deleteField = async (fieldId) => {
    const index = fields.value.findIndex(f => f.id === fieldId)
    if (index === -1) return
    
    const field = fields.value[index]
    
    // If it's an existing field (not a temporary one), delete from server
    if (field.id && !field.id.toString().startsWith('field_')) {
      try {
        await axios.delete(`${apiBaseUrl}/fields/${field.id}/`)
      } catch (err) {
        console.error('Error deleting field:', err)
        throw err
      }
    }
    
    // Remove from local array
    fields.value.splice(index, 1)
    
    // Update order of remaining fields
    fields.value.forEach((field, idx) => {
      field.order = idx + 1
    })
  }
  
  // Reorder fields
  const reorderFields = (newFields) => {
    fields.value = newFields.map((field, index) => ({
      ...field,
      order: index + 1
    }))
  }
  
  // Duplicate field
  const duplicateField = (field) => {
    const duplicatedField = {
      ...field,
      id: `field_${Date.now()}`,
      name: `${field.name}_copy`,
      label: `${field.label} (Copy)`,
      order: field.order + 1
    }
    
    // Insert after the original field
    const originalIndex = fields.value.findIndex(f => f.id === field.id)
    fields.value.splice(originalIndex + 1, 0, duplicatedField)
    
    // Update order of subsequent fields
    fields.value.forEach((field, index) => {
      field.order = index + 1
    })
    
    return duplicatedField
  }
  
  // Helper functions
  const getDefaultLabel = (fieldType) => {
    const labels = {
      text: 'Text Input',
      textarea: 'Text Area',
      email: 'Email Address',
      url: 'Website URL',
      number: 'Number',
      date: 'Date',
      time: 'Time',
      datetime: 'Date & Time',
      select: 'Dropdown',
      radio: 'Radio Buttons',
      checkbox: 'Checkboxes',
      checkbox_single: 'Checkbox',
      file: 'File Upload',
      image: 'Image Upload',
      rating: 'Rating',
      slider: 'Slider',
      signature: 'Signature',
      rich_text: 'Rich Text Editor',
      color: 'Color Picker',
      phone: 'Phone Number',
      address: 'Address',
      matrix: 'Matrix Question',
      section: 'Section Header',
      html: 'HTML Content',
      hidden: 'Hidden Field'
    }
    
    return labels[fieldType] || 'Form Field'
  }
  
  const getDefaultProperties = (fieldType) => {
    const defaults = {
      text: { max_length: 255 },
      textarea: { rows: 4, max_length: 2000 },
      email: { validate_email: true },
      url: { validate_url: true },
      number: { min_value: null, max_value: null, step: 1 },
      date: { date_format: 'YYYY-MM-DD' },
      time: { time_format: 'HH:MM' },
      select: { options: [], multiple: false },
      radio: { options: [], layout: 'vertical' },
      checkbox: { options: [], min_selections: null, max_selections: null },
      file: { 
        max_file_size: 5242880, 
        allowed_extensions: ['pdf', 'doc', 'docx'], 
        multiple: false 
      },
      image: { 
        max_file_size: 2097152, 
        allowed_extensions: ['jpg', 'jpeg', 'png'], 
        multiple: false 
      },
      rating: { min_rating: 1, max_rating: 5, step: 1 },
      slider: { min_value: 0, max_value: 100, step: 1, show_value: true },
      signature: {
        width: 400,
        height: 200,
        pen_color: '#000000',
        background_color: '#ffffff'
      },
      rich_text: {
        toolbar: ['bold', 'italic', 'underline', 'bulletList', 'numberedList'],
        max_length: 5000
      },
      color: {
        default_color: '#3B82F6',
        format: 'hex'
      },
      phone: {
        default_country: 'US',
        format: 'international',
        validate_format: true
      },
      address: {
        fields: ['street', 'city', 'state', 'zip', 'country'],
        required_fields: ['street', 'city', 'state', 'zip'],
        default_country: 'US'
      },
      matrix: {
        rows: ['Question 1', 'Question 2', 'Question 3'],
        columns: ['Strongly Disagree', 'Disagree', 'Neutral', 'Agree', 'Strongly Agree'],
        input_type: 'radio'
      },
      section: { level: 2 },
      html: { content: '<p>Enter your HTML content here</p>' }
    }
    
    return defaults[fieldType] || {}
  }
  
  return {
    form,
    fields,
    loading,
    error,
    loadForm,
    saveForm,
    addField,
    updateField,
    deleteField,
    reorderFields,
    duplicateField
  }
}
