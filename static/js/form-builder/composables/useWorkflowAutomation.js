import { ref, reactive, computed } from 'vue'

export function useWorkflowAutomation() {
  const workflows = ref([])
  const activeWorkflows = reactive(new Map())
  const workflowHistory = ref([])
  const isProcessing = ref(false)
  
  // Workflow trigger types
  const triggerTypes = {
    FORM_SUBMIT: {
      name: 'Form Submission',
      description: 'Triggered when form is submitted',
      icon: 'submit',
      conditions: ['always', 'conditional']
    },
    FIELD_CHANGE: {
      name: 'Field Change',
      description: 'Triggered when specific field value changes',
      icon: 'edit',
      conditions: ['value_equals', 'value_contains', 'value_greater', 'value_less']
    },
    PAGE_CHANGE: {
      name: 'Page Change',
      description: 'Triggered when user navigates between pages',
      icon: 'navigation',
      conditions: ['page_enter', 'page_exit']
    },
    TIME_BASED: {
      name: 'Time-based',
      description: 'Triggered after specific time delay',
      icon: 'clock',
      conditions: ['delay', 'schedule']
    },
    EXTERNAL_EVENT: {
      name: 'External Event',
      description: 'Triggered by external webhook or API call',
      icon: 'webhook',
      conditions: ['webhook_received', 'api_called']
    }
  }
  
  // Action types
  const actionTypes = {
    SEND_EMAIL: {
      name: 'Send Email',
      description: 'Send email notification',
      icon: 'email',
      params: ['to', 'subject', 'template', 'attachments']
    },
    CREATE_RECORD: {
      name: 'Create Database Record',
      description: 'Create record in database or external system',
      icon: 'database',
      params: ['table', 'fields', 'connection']
    },
    UPDATE_RECORD: {
      name: 'Update Record',
      description: 'Update existing record',
      icon: 'edit',
      params: ['table', 'identifier', 'fields', 'connection']
    },
    WEBHOOK_CALL: {
      name: 'Webhook Call',
      description: 'Send HTTP request to external endpoint',
      icon: 'webhook',
      params: ['url', 'method', 'headers', 'body']
    },
    ASSIGN_TASK: {
      name: 'Assign Task',
      description: 'Create task assignment',
      icon: 'task',
      params: ['assignee', 'title', 'description', 'dueDate']
    },
    SEND_SMS: {
      name: 'Send SMS',
      description: 'Send SMS notification',
      icon: 'sms',
      params: ['to', 'message', 'provider']
    },
    GENERATE_PDF: {
      name: 'Generate PDF',
      description: 'Generate PDF document from form data',
      icon: 'pdf',
      params: ['template', 'filename', 'destination']
    },
    CONDITIONAL_LOGIC: {
      name: 'Conditional Logic',
      description: 'Execute actions based on conditions',
      icon: 'branch',
      params: ['conditions', 'trueActions', 'falseActions']
    },
    DELAY: {
      name: 'Delay',
      description: 'Wait for specified time before next action',
      icon: 'clock',
      params: ['duration', 'unit']
    },
    INTEGRATION_SYNC: {
      name: 'Integration Sync',
      description: 'Sync data with external integration',
      icon: 'sync',
      params: ['integration', 'action', 'mapping']
    }
  }
  
  // Create new workflow
  const createWorkflow = (config) => {
    const workflow = {
      id: `workflow-${Date.now()}`,
      name: config.name,
      description: config.description,
      isActive: true,
      trigger: {
        type: config.trigger.type,
        conditions: config.trigger.conditions || {},
        settings: config.trigger.settings || {}
      },
      actions: config.actions || [],
      settings: {
        runOnce: false,
        maxExecutions: null,
        timeout: 30000, // 30 seconds
        retryAttempts: 3,
        retryDelay: 1000,
        ...config.settings
      },
      statistics: {
        executions: 0,
        successes: 0,
        failures: 0,
        lastRun: null,
        averageExecutionTime: 0
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    workflows.value.push(workflow)
    return workflow
  }
  
  // Execute workflow
  const executeWorkflow = async (workflowId, triggerData = {}) => {
    const workflow = workflows.value.find(w => w.id === workflowId)
    if (!workflow || !workflow.isActive) return
    
    // Check execution limits
    if (workflow.settings.maxExecutions && 
        workflow.statistics.executions >= workflow.settings.maxExecutions) {
      return
    }
    
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()
    
    isProcessing.value = true
    activeWorkflows.set(executionId, {
      workflowId,
      status: 'running',
      startTime,
      currentAction: 0,
      triggerData
    })
    
    try {
      const context = {
        workflowId,
        executionId,
        triggerData,
        formData: triggerData.formData || {},
        variables: {},
        results: []
      }
      
      // Execute actions sequentially
      for (let i = 0; i < workflow.actions.length; i++) {
        const action = workflow.actions[i]
        
        // Update current action
        activeWorkflows.get(executionId).currentAction = i
        
        try {
          const result = await executeAction(action, context)
          context.results.push({
            actionIndex: i,
            actionType: action.type,
            success: true,
            result,
            executedAt: Date.now()
          })
          
          // Store result in variables for next actions
          if (action.outputVariable) {
            context.variables[action.outputVariable] = result
          }
          
        } catch (error) {
          console.error(`Action ${i} failed:`, error)
          
          context.results.push({
            actionIndex: i,
            actionType: action.type,
            success: false,
            error: error.message,
            executedAt: Date.now()
          })
          
          // Handle error based on action settings
          if (action.continueOnError) {
            continue
          } else {
            throw error
          }
        }
      }
      
      // Update statistics
      const executionTime = Date.now() - startTime
      workflow.statistics.executions++
      workflow.statistics.successes++
      workflow.statistics.lastRun = new Date().toISOString()
      workflow.statistics.averageExecutionTime = 
        (workflow.statistics.averageExecutionTime + executionTime) / workflow.statistics.executions
      
      // Add to history
      workflowHistory.value.unshift({
        id: executionId,
        workflowId,
        workflowName: workflow.name,
        status: 'completed',
        startTime,
        endTime: Date.now(),
        executionTime,
        triggerData,
        results: context.results,
        actionsExecuted: context.results.length
      })
      
      activeWorkflows.delete(executionId)
      
    } catch (error) {
      console.error(`Workflow ${workflowId} failed:`, error)
      
      // Update statistics
      workflow.statistics.executions++
      workflow.statistics.failures++
      workflow.statistics.lastRun = new Date().toISOString()
      
      // Add to history
      workflowHistory.value.unshift({
        id: executionId,
        workflowId,
        workflowName: workflow.name,
        status: 'failed',
        startTime,
        endTime: Date.now(),
        executionTime: Date.now() - startTime,
        triggerData,
        error: error.message,
        actionsExecuted: activeWorkflows.get(executionId)?.currentAction || 0
      })
      
      activeWorkflows.delete(executionId)
      
      // Retry if configured
      if (workflow.settings.retryAttempts > 0) {
        setTimeout(() => {
          workflow.settings.retryAttempts--
          executeWorkflow(workflowId, triggerData)
        }, workflow.settings.retryDelay)
      }
      
    } finally {
      isProcessing.value = false
    }
  }
  
  // Execute individual action
  const executeAction = async (action, context) => {
    const { type, params } = action
    
    switch (type) {
      case 'SEND_EMAIL':
        return await executeSendEmail(params, context)
      case 'CREATE_RECORD':
        return await executeCreateRecord(params, context)
      case 'UPDATE_RECORD':
        return await executeUpdateRecord(params, context)
      case 'WEBHOOK_CALL':
        return await executeWebhookCall(params, context)
      case 'ASSIGN_TASK':
        return await executeAssignTask(params, context)
      case 'SEND_SMS':
        return await executeSendSMS(params, context)
      case 'GENERATE_PDF':
        return await executeGeneratePDF(params, context)
      case 'CONDITIONAL_LOGIC':
        return await executeConditionalLogic(params, context)
      case 'DELAY':
        return await executeDelay(params, context)
      case 'INTEGRATION_SYNC':
        return await executeIntegrationSync(params, context)
      default:
        throw new Error(`Unknown action type: ${type}`)
    }
  }
  
  // Action implementations
  const executeSendEmail = async (params, context) => {
    const { to, subject, template, attachments } = params
    
    // Replace variables in template
    const processedSubject = replaceVariables(subject, context)
    const processedTo = replaceVariables(to, context)
    
    // Mock email sending
    console.log('Sending email:', { to: processedTo, subject: processedSubject })
    
    return {
      emailId: `email-${Date.now()}`,
      to: processedTo,
      subject: processedSubject,
      sentAt: new Date().toISOString()
    }
  }
  
  const executeCreateRecord = async (params, context) => {
    const { table, fields, connection } = params
    
    // Process field values
    const processedFields = {}
    for (const [key, value] of Object.entries(fields)) {
      processedFields[key] = replaceVariables(value, context)
    }
    
    // Mock record creation
    console.log('Creating record:', { table, fields: processedFields })
    
    return {
      recordId: `record-${Date.now()}`,
      table,
      fields: processedFields,
      createdAt: new Date().toISOString()
    }
  }
  
  const executeWebhookCall = async (params, context) => {
    const { url, method = 'POST', headers = {}, body } = params
    
    const processedUrl = replaceVariables(url, context)
    const processedBody = replaceVariables(JSON.stringify(body), context)
    
    const response = await fetch(processedUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: method !== 'GET' ? processedBody : undefined
    })
    
    if (!response.ok) {
      throw new Error(`Webhook call failed: ${response.statusText}`)
    }
    
    const result = await response.json()
    return {
      status: response.status,
      data: result,
      calledAt: new Date().toISOString()
    }
  }
  
  const executeConditionalLogic = async (params, context) => {
    const { conditions, trueActions, falseActions } = params
    
    // Evaluate conditions
    const conditionMet = evaluateConditions(conditions, context)
    
    // Execute appropriate actions
    const actionsToExecute = conditionMet ? trueActions : falseActions
    const results = []
    
    for (const action of actionsToExecute || []) {
      const result = await executeAction(action, context)
      results.push(result)
    }
    
    return {
      conditionMet,
      actionsExecuted: results.length,
      results
    }
  }
  
  const executeDelay = async (params, context) => {
    const { duration, unit = 'seconds' } = params
    
    const multipliers = {
      milliseconds: 1,
      seconds: 1000,
      minutes: 60000,
      hours: 3600000
    }
    
    const delayMs = duration * (multipliers[unit] || 1000)
    
    await new Promise(resolve => setTimeout(resolve, delayMs))
    
    return {
      delayed: delayMs,
      unit,
      completedAt: new Date().toISOString()
    }
  }
  
  // Utility functions
  const replaceVariables = (template, context) => {
    if (typeof template !== 'string') return template
    
    return template.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
      const path = variable.trim().split('.')
      let value = context
      
      for (const key of path) {
        value = value?.[key]
      }
      
      return value !== undefined ? String(value) : match
    })
  }
  
  const evaluateConditions = (conditions, context) => {
    if (!conditions || conditions.length === 0) return true
    
    const results = conditions.map(condition => {
      const { field, operator, value } = condition
      const fieldValue = getNestedValue(context, field)
      
      switch (operator) {
        case 'equals':
          return fieldValue === value
        case 'not_equals':
          return fieldValue !== value
        case 'contains':
          return String(fieldValue || '').includes(value)
        case 'greater_than':
          return Number(fieldValue) > Number(value)
        case 'less_than':
          return Number(fieldValue) < Number(value)
        case 'is_empty':
          return !fieldValue || fieldValue === ''
        case 'is_not_empty':
          return fieldValue && fieldValue !== ''
        default:
          return true
      }
    })
    
    return conditions.operator === 'OR' 
      ? results.some(r => r)
      : results.every(r => r)
  }
  
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }
  
  // Trigger workflow based on event
  const triggerWorkflows = async (eventType, eventData) => {
    const matchingWorkflows = workflows.value.filter(workflow => 
      workflow.isActive && workflow.trigger.type === eventType
    )
    
    for (const workflow of matchingWorkflows) {
      // Check if trigger conditions are met
      if (shouldTriggerWorkflow(workflow, eventData)) {
        await executeWorkflow(workflow.id, eventData)
      }
    }
  }
  
  const shouldTriggerWorkflow = (workflow, eventData) => {
    const { conditions } = workflow.trigger
    
    if (!conditions || Object.keys(conditions).length === 0) {
      return true // Always trigger if no conditions
    }
    
    return evaluateConditions([conditions], { triggerData: eventData })
  }
  
  // Get workflow statistics
  const getWorkflowStats = () => {
    const totalWorkflows = workflows.value.length
    const activeWorkflows = workflows.value.filter(w => w.isActive).length
    const totalExecutions = workflows.value.reduce((sum, w) => sum + w.statistics.executions, 0)
    const totalSuccesses = workflows.value.reduce((sum, w) => sum + w.statistics.successes, 0)
    const totalFailures = workflows.value.reduce((sum, w) => sum + w.statistics.failures, 0)
    
    return {
      totalWorkflows,
      activeWorkflows,
      totalExecutions,
      totalSuccesses,
      totalFailures,
      successRate: totalExecutions > 0 ? (totalSuccesses / totalExecutions) * 100 : 0
    }
  }
  
  // Export workflow configuration
  const exportWorkflow = (workflowId) => {
    const workflow = workflows.value.find(w => w.id === workflowId)
    if (!workflow) return null
    
    return {
      ...workflow,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    }
  }
  
  // Import workflow configuration
  const importWorkflow = (workflowConfig) => {
    const workflow = {
      ...workflowConfig,
      id: `workflow-${Date.now()}`,
      statistics: {
        executions: 0,
        successes: 0,
        failures: 0,
        lastRun: null,
        averageExecutionTime: 0
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    workflows.value.push(workflow)
    return workflow
  }
  
  return {
    workflows,
    activeWorkflows,
    workflowHistory,
    isProcessing,
    triggerTypes,
    actionTypes,
    createWorkflow,
    executeWorkflow,
    triggerWorkflows,
    getWorkflowStats,
    exportWorkflow,
    importWorkflow
  }
}
