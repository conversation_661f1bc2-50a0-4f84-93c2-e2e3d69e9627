import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

export function useAccessibility() {
  const isHighContrast = ref(false)
  const isReducedMotion = ref(false)
  const fontSize = ref(16)
  const focusVisible = ref(true)
  const screenReaderMode = ref(false)
  const keyboardNavigation = ref(true)
  
  const accessibilitySettings = reactive({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    focusIndicators: true,
    screenReaderOptimized: false,
    keyboardOnly: false,
    colorBlindFriendly: false,
    announcements: true
  })
  
  const announcements = ref([])
  const focusHistory = ref([])
  const currentFocusIndex = ref(-1)
  
  // ARIA live region for announcements
  const liveRegion = ref(null)
  
  // Accessibility compliance levels
  const complianceLevel = computed(() => {
    const checks = [
      accessibilitySettings.focusIndicators,
      accessibilitySettings.announcements,
      !hasColorOnlyIndicators(),
      hasProperHeadings(),
      hasAltText(),
      hasFormLabels(),
      hasKeyboardSupport()
    ]
    
    const passedChecks = checks.filter(Boolean).length
    const percentage = (passedChecks / checks.length) * 100
    
    if (percentage >= 90) return 'AAA'
    if (percentage >= 80) return 'AA'
    if (percentage >= 60) return 'A'
    return 'Non-compliant'
  })
  
  // Initialize accessibility features
  const initializeAccessibility = () => {
    // Check for user preferences
    checkSystemPreferences()
    
    // Load saved settings
    loadAccessibilitySettings()
    
    // Create ARIA live region
    createLiveRegion()
    
    // Set up keyboard navigation
    setupKeyboardNavigation()
    
    // Apply initial settings
    applyAccessibilitySettings()
  }
  
  // Check system accessibility preferences
  const checkSystemPreferences = () => {
    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      isReducedMotion.value = true
      accessibilitySettings.reducedMotion = true
    }
    
    // Check for high contrast preference
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      isHighContrast.value = true
      accessibilitySettings.highContrast = true
    }
    
    // Check for color scheme preference
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      accessibilitySettings.highContrast = true
    }
    
    // Listen for changes
    window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
      isReducedMotion.value = e.matches
      accessibilitySettings.reducedMotion = e.matches
      applyAccessibilitySettings()
    })
    
    window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
      isHighContrast.value = e.matches
      accessibilitySettings.highContrast = e.matches
      applyAccessibilitySettings()
    })
  }
  
  // Create ARIA live region for announcements
  const createLiveRegion = () => {
    if (typeof document === 'undefined') return
    
    const region = document.createElement('div')
    region.setAttribute('aria-live', 'polite')
    region.setAttribute('aria-atomic', 'true')
    region.setAttribute('class', 'sr-only')
    region.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `
    
    document.body.appendChild(region)
    liveRegion.value = region
  }
  
  // Announce message to screen readers
  const announce = (message, priority = 'polite') => {
    if (!accessibilitySettings.announcements || !liveRegion.value) return
    
    const announcement = {
      id: Date.now(),
      message,
      priority,
      timestamp: new Date().toISOString()
    }
    
    announcements.value.push(announcement)
    
    // Update live region
    liveRegion.value.setAttribute('aria-live', priority)
    liveRegion.value.textContent = message
    
    // Clear after announcement
    setTimeout(() => {
      if (liveRegion.value) {
        liveRegion.value.textContent = ''
      }
    }, 1000)
    
    // Keep only recent announcements
    if (announcements.value.length > 10) {
      announcements.value = announcements.value.slice(-10)
    }
  }
  
  // Enhanced focus management
  const manageFocus = (element, options = {}) => {
    if (!element) return
    
    const { 
      preventScroll = false, 
      announce: shouldAnnounce = false,
      restoreFocus = true 
    } = options
    
    // Store current focus for restoration
    if (restoreFocus && document.activeElement) {
      focusHistory.value.push(document.activeElement)
    }
    
    // Focus the element
    element.focus({ preventScroll })
    
    // Announce focus change if requested
    if (shouldAnnounce) {
      const label = element.getAttribute('aria-label') || 
                   element.getAttribute('title') || 
                   element.textContent?.trim() ||
                   'Element'
      announce(`Focused on ${label}`)
    }
    
    // Add visual focus indicator if needed
    if (accessibilitySettings.focusIndicators) {
      element.classList.add('a11y-focus')
      
      // Remove on blur
      const handleBlur = () => {
        element.classList.remove('a11y-focus')
        element.removeEventListener('blur', handleBlur)
      }
      element.addEventListener('blur', handleBlur)
    }
  }
  
  // Restore previous focus
  const restoreFocus = () => {
    if (focusHistory.value.length > 0) {
      const previousElement = focusHistory.value.pop()
      if (previousElement && document.contains(previousElement)) {
        manageFocus(previousElement, { announce: false, restoreFocus: false })
      }
    }
  }
  
  // Setup keyboard navigation
  const setupKeyboardNavigation = () => {
    if (typeof document === 'undefined') return
    
    // Enhanced tab navigation
    document.addEventListener('keydown', (event) => {
      if (!keyboardNavigation.value) return
      
      // Skip links navigation
      if (event.key === 'Tab' && event.ctrlKey) {
        event.preventDefault()
        showSkipLinks()
      }
      
      // Focus visible indicator
      if (event.key === 'Tab') {
        document.body.classList.add('keyboard-navigation')
      }
      
      // Escape key handling
      if (event.key === 'Escape') {
        handleEscapeKey(event)
      }
      
      // Arrow key navigation in grids/lists
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        handleArrowNavigation(event)
      }
    })
    
    // Remove keyboard navigation class on mouse use
    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation')
    })
  }
  
  // Handle escape key for modal/dialog dismissal
  const handleEscapeKey = (event) => {
    const activeModal = document.querySelector('[role="dialog"]:not([aria-hidden="true"])')
    const activeDropdown = document.querySelector('[aria-expanded="true"]')
    
    if (activeModal) {
      // Close modal and restore focus
      const closeButton = activeModal.querySelector('[data-dismiss="modal"]')
      if (closeButton) {
        closeButton.click()
      }
      event.preventDefault()
    } else if (activeDropdown) {
      // Close dropdown
      activeDropdown.setAttribute('aria-expanded', 'false')
      manageFocus(activeDropdown)
      event.preventDefault()
    }
  }
  
  // Handle arrow key navigation
  const handleArrowNavigation = (event) => {
    const target = event.target
    const role = target.getAttribute('role')
    
    if (role === 'gridcell' || role === 'option' || role === 'menuitem') {
      event.preventDefault()
      
      const container = target.closest('[role="grid"], [role="listbox"], [role="menu"]')
      if (!container) return
      
      const items = Array.from(container.querySelectorAll(`[role="${role}"]`))
      const currentIndex = items.indexOf(target)
      
      let nextIndex = currentIndex
      
      switch (event.key) {
        case 'ArrowUp':
          nextIndex = Math.max(0, currentIndex - 1)
          break
        case 'ArrowDown':
          nextIndex = Math.min(items.length - 1, currentIndex + 1)
          break
        case 'ArrowLeft':
          if (role === 'gridcell') {
            // Navigate to previous cell in row
            nextIndex = Math.max(0, currentIndex - 1)
          }
          break
        case 'ArrowRight':
          if (role === 'gridcell') {
            // Navigate to next cell in row
            nextIndex = Math.min(items.length - 1, currentIndex + 1)
          }
          break
      }
      
      if (nextIndex !== currentIndex && items[nextIndex]) {
        manageFocus(items[nextIndex], { announce: true })
      }
    }
  }
  
  // Show skip links
  const showSkipLinks = () => {
    const skipLinks = document.querySelector('.skip-links')
    if (skipLinks) {
      skipLinks.classList.add('visible')
      const firstLink = skipLinks.querySelector('a')
      if (firstLink) {
        manageFocus(firstLink)
      }
    }
  }
  
  // Apply accessibility settings
  const applyAccessibilitySettings = () => {
    const root = document.documentElement
    
    // High contrast mode
    if (accessibilitySettings.highContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }
    
    // Reduced motion
    if (accessibilitySettings.reducedMotion) {
      root.classList.add('reduced-motion')
    } else {
      root.classList.remove('reduced-motion')
    }
    
    // Large text
    if (accessibilitySettings.largeText) {
      root.classList.add('large-text')
      fontSize.value = 20
    } else {
      root.classList.remove('large-text')
      fontSize.value = 16
    }
    
    // Color blind friendly
    if (accessibilitySettings.colorBlindFriendly) {
      root.classList.add('color-blind-friendly')
    } else {
      root.classList.remove('color-blind-friendly')
    }
    
    // Screen reader optimized
    if (accessibilitySettings.screenReaderOptimized) {
      root.classList.add('screen-reader-optimized')
      screenReaderMode.value = true
    } else {
      root.classList.remove('screen-reader-optimized')
      screenReaderMode.value = false
    }
    
    // Focus indicators
    focusVisible.value = accessibilitySettings.focusIndicators
  }
  
  // Accessibility audit functions
  const hasColorOnlyIndicators = () => {
    // Check if any elements rely solely on color for meaning
    const colorOnlyElements = document.querySelectorAll('.error, .success, .warning')
    return Array.from(colorOnlyElements).some(el => {
      const hasIcon = el.querySelector('svg, .icon, [class*="icon"]')
      const hasText = el.textContent.trim().length > 0
      return !hasIcon && !hasText
    })
  }
  
  const hasProperHeadings = () => {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    if (headings.length === 0) return false
    
    // Check for proper heading hierarchy
    let previousLevel = 0
    for (const heading of headings) {
      const level = parseInt(heading.tagName.charAt(1))
      if (level > previousLevel + 1) return false
      previousLevel = level
    }
    
    return true
  }
  
  const hasAltText = () => {
    const images = document.querySelectorAll('img')
    return Array.from(images).every(img => 
      img.hasAttribute('alt') || img.hasAttribute('aria-label')
    )
  }
  
  const hasFormLabels = () => {
    const inputs = document.querySelectorAll('input, select, textarea')
    return Array.from(inputs).every(input => {
      const hasLabel = document.querySelector(`label[for="${input.id}"]`)
      const hasAriaLabel = input.hasAttribute('aria-label')
      const hasAriaLabelledby = input.hasAttribute('aria-labelledby')
      return hasLabel || hasAriaLabel || hasAriaLabelledby
    })
  }
  
  const hasKeyboardSupport = () => {
    const interactiveElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]')
    return Array.from(interactiveElements).every(el => {
      const tabIndex = el.getAttribute('tabindex')
      return tabIndex !== '-1' || el.hasAttribute('aria-hidden')
    })
  }
  
  // Save accessibility settings
  const saveAccessibilitySettings = () => {
    localStorage.setItem('formBuilderA11ySettings', JSON.stringify(accessibilitySettings))
  }
  
  // Load accessibility settings
  const loadAccessibilitySettings = () => {
    try {
      const saved = localStorage.getItem('formBuilderA11ySettings')
      if (saved) {
        const settings = JSON.parse(saved)
        Object.assign(accessibilitySettings, settings)
      }
    } catch (error) {
      console.warn('Failed to load accessibility settings:', error)
    }
  }
  
  // Update settings and apply changes
  const updateAccessibilitySettings = (newSettings) => {
    Object.assign(accessibilitySettings, newSettings)
    applyAccessibilitySettings()
    saveAccessibilitySettings()
    
    announce('Accessibility settings updated')
  }
  
  // Generate accessibility report
  const generateAccessibilityReport = () => {
    return {
      complianceLevel: complianceLevel.value,
      checks: {
        colorOnlyIndicators: !hasColorOnlyIndicators(),
        properHeadings: hasProperHeadings(),
        altText: hasAltText(),
        formLabels: hasFormLabels(),
        keyboardSupport: hasKeyboardSupport()
      },
      settings: { ...accessibilitySettings },
      announcements: announcements.value.slice(-5),
      timestamp: new Date().toISOString()
    }
  }
  
  return {
    accessibilitySettings,
    complianceLevel,
    isHighContrast,
    isReducedMotion,
    fontSize,
    focusVisible,
    screenReaderMode,
    keyboardNavigation,
    announcements,
    initializeAccessibility,
    announce,
    manageFocus,
    restoreFocus,
    updateAccessibilitySettings,
    generateAccessibilityReport
  }
}
