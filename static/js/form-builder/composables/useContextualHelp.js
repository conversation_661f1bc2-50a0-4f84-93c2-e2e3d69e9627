import { ref, reactive, computed, watch } from 'vue'

export function useContextualHelp() {
  const isHelpMode = ref(false)
  const currentContext = ref(null)
  const helpHistory = ref([])
  const userPreferences = reactive({
    showTooltips: true,
    showHints: true,
    autoShowHelp: true,
    helpLevel: 'intermediate' // beginner, intermediate, advanced
  })
  
  // Help content database
  const helpContent = reactive({
    // Field Types Help
    'field-text': {
      title: 'Text Input Field',
      description: 'A single-line text input for collecting short text responses.',
      tips: [
        'Use placeholder text to guide users',
        'Set character limits for consistent data',
        'Consider validation rules for data quality'
      ],
      examples: ['Name', 'Email', 'Phone number'],
      bestPractices: [
        'Keep labels clear and concise',
        'Use appropriate input types (email, tel, etc.)',
        'Provide helpful error messages'
      ],
      relatedFields: ['email', 'password', 'url'],
      shortcuts: ['Ctrl+1'],
      difficulty: 'beginner'
    },
    
    'field-textarea': {
      title: 'Textarea Field',
      description: 'A multi-line text input for collecting longer text responses.',
      tips: [
        'Set appropriate rows and columns',
        'Consider character or word limits',
        'Use for comments, descriptions, or feedback'
      ],
      examples: ['Comments', 'Description', 'Feedback'],
      bestPractices: [
        'Provide clear instructions about expected length',
        'Consider rich text editor for formatted content',
        'Use auto-resize for better UX'
      ],
      relatedFields: ['text', 'rich-text'],
      shortcuts: ['Ctrl+2'],
      difficulty: 'beginner'
    },
    
    'field-select': {
      title: 'Select Dropdown',
      description: 'A dropdown menu for selecting one option from a predefined list.',
      tips: [
        'Order options logically (alphabetical, frequency, etc.)',
        'Include a default "Please select" option',
        'Consider searchable dropdowns for long lists'
      ],
      examples: ['Country', 'Department', 'Priority level'],
      bestPractices: [
        'Limit to 7-15 options when possible',
        'Use radio buttons for 2-5 options',
        'Group related options with optgroups'
      ],
      relatedFields: ['radio', 'checkbox'],
      shortcuts: ['Ctrl+3'],
      difficulty: 'beginner'
    },
    
    'field-radio': {
      title: 'Radio Buttons',
      description: 'Multiple choice field where users can select only one option.',
      tips: [
        'Use for 2-5 mutually exclusive options',
        'Always have one option pre-selected',
        'Arrange vertically for better readability'
      ],
      examples: ['Gender', 'Yes/No questions', 'Rating scale'],
      bestPractices: [
        'Keep option text short and clear',
        'Use logical ordering',
        'Consider using "Other" with text input'
      ],
      relatedFields: ['select', 'checkbox'],
      shortcuts: ['Ctrl+4'],
      difficulty: 'beginner'
    },
    
    'field-checkbox': {
      title: 'Checkboxes',
      description: 'Multiple choice field where users can select multiple options.',
      tips: [
        'Use for non-exclusive multiple selections',
        'Consider "Select All" option for long lists',
        'Group related options together'
      ],
      examples: ['Interests', 'Skills', 'Features'],
      bestPractices: [
        'Limit to 10-15 options when possible',
        'Use clear, parallel phrasing',
        'Consider minimum/maximum selection limits'
      ],
      relatedFields: ['radio', 'select'],
      shortcuts: ['Ctrl+5'],
      difficulty: 'beginner'
    },
    
    // Form Builder Features
    'drag-drop': {
      title: 'Drag & Drop',
      description: 'Drag fields from the palette to the form canvas to add them.',
      tips: [
        'Drag fields to specific positions',
        'Use drop zones for precise placement',
        'Reorder fields by dragging within the canvas'
      ],
      bestPractices: [
        'Plan your form structure before building',
        'Group related fields together',
        'Use logical field ordering'
      ],
      shortcuts: ['Click and drag'],
      difficulty: 'beginner'
    },
    
    'conditional-logic': {
      title: 'Conditional Logic',
      description: 'Show or hide fields based on user responses to other fields.',
      tips: [
        'Start with simple conditions',
        'Test all logic paths thoroughly',
        'Use clear field dependencies'
      ],
      examples: [
        'Show address fields only if "Ship to different address" is checked',
        'Show follow-up questions based on initial response'
      ],
      bestPractices: [
        'Keep logic simple and intuitive',
        'Avoid circular dependencies',
        'Provide clear visual feedback'
      ],
      difficulty: 'advanced'
    },
    
    'validation': {
      title: 'Field Validation',
      description: 'Set rules to ensure data quality and completeness.',
      tips: [
        'Use appropriate validation for each field type',
        'Provide clear error messages',
        'Validate on both client and server'
      ],
      examples: [
        'Required fields',
        'Email format validation',
        'Minimum/maximum values'
      ],
      bestPractices: [
        'Show validation errors inline',
        'Use positive language in error messages',
        'Validate as users type when appropriate'
      ],
      difficulty: 'intermediate'
    },
    
    // Advanced Features
    'multi-page': {
      title: 'Multi-Page Forms',
      description: 'Break long forms into multiple pages for better user experience.',
      tips: [
        'Group related fields on the same page',
        'Show progress indicators',
        'Allow users to go back and edit'
      ],
      bestPractices: [
        'Keep pages focused on single topics',
        'Use descriptive page titles',
        'Save progress automatically'
      ],
      difficulty: 'advanced'
    },
    
    'integrations': {
      title: 'External Integrations',
      description: 'Connect your form to external services and databases.',
      tips: [
        'Test connections before going live',
        'Handle API failures gracefully',
        'Map fields correctly'
      ],
      bestPractices: [
        'Use secure authentication methods',
        'Implement proper error handling',
        'Monitor integration health'
      ],
      difficulty: 'advanced'
    }
  })
  
  // Context-aware help suggestions
  const contextualSuggestions = computed(() => {
    if (!currentContext.value) return []
    
    const context = currentContext.value
    const suggestions = []
    
    // Based on current field type
    if (context.fieldType) {
      const fieldHelp = helpContent[`field-${context.fieldType}`]
      if (fieldHelp) {
        suggestions.push({
          type: 'field-help',
          title: fieldHelp.title,
          description: fieldHelp.description,
          priority: 'high'
        })
      }
    }
    
    // Based on current action
    if (context.action) {
      switch (context.action) {
        case 'adding-field':
          suggestions.push({
            type: 'tip',
            title: 'Field Placement',
            description: 'Drag fields to specific positions or use the drop zones for precise placement.',
            priority: 'medium'
          })
          break
        case 'configuring-field':
          suggestions.push({
            type: 'tip',
            title: 'Field Configuration',
            description: 'Use the properties panel to customize field behavior and appearance.',
            priority: 'high'
          })
          break
        case 'preview-mode':
          suggestions.push({
            type: 'tip',
            title: 'Testing Your Form',
            description: 'Try filling out the form as a user would to test the experience.',
            priority: 'medium'
          })
          break
      }
    }
    
    // Based on form complexity
    if (context.formStats) {
      const { fieldCount, hasConditionalLogic, hasValidation } = context.formStats
      
      if (fieldCount > 10 && !context.hasMultiPage) {
        suggestions.push({
          type: 'recommendation',
          title: 'Consider Multi-Page Form',
          description: 'Forms with many fields work better when split into multiple pages.',
          priority: 'medium'
        })
      }
      
      if (fieldCount > 5 && !hasValidation) {
        suggestions.push({
          type: 'recommendation',
          title: 'Add Validation Rules',
          description: 'Validation helps ensure data quality and improves user experience.',
          priority: 'low'
        })
      }
    }
    
    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  })
  
  // Smart help based on user behavior
  const behaviorBasedHelp = computed(() => {
    const suggestions = []
    
    // Check for common patterns in help history
    const recentHelp = helpHistory.value.slice(-10)
    const helpTopics = recentHelp.map(h => h.topic)
    
    // If user keeps looking up field types, suggest field guide
    const fieldTypeQueries = helpTopics.filter(topic => topic.startsWith('field-'))
    if (fieldTypeQueries.length >= 3) {
      suggestions.push({
        type: 'guide',
        title: 'Field Types Guide',
        description: 'Learn about all available field types and when to use them.',
        action: 'show-field-guide'
      })
    }
    
    // If user is struggling with advanced features
    const advancedQueries = recentHelp.filter(h => 
      helpContent[h.topic]?.difficulty === 'advanced'
    )
    if (advancedQueries.length >= 2) {
      suggestions.push({
        type: 'tutorial',
        title: 'Advanced Features Tutorial',
        description: 'Step-by-step guide to advanced form building features.',
        action: 'start-advanced-tutorial'
      })
    }
    
    return suggestions
  })
  
  // Update context based on current state
  const updateContext = (newContext) => {
    currentContext.value = {
      ...currentContext.value,
      ...newContext,
      timestamp: Date.now()
    }
  }
  
  // Get help for specific topic
  const getHelp = (topic) => {
    const help = helpContent[topic]
    if (help) {
      // Filter content based on user level
      const filteredHelp = {
        ...help,
        tips: help.tips || [],
        bestPractices: userPreferences.helpLevel === 'beginner' 
          ? (help.bestPractices || []).slice(0, 3)
          : help.bestPractices || []
      }
      
      // Add to history
      helpHistory.value.push({
        topic,
        timestamp: Date.now(),
        context: currentContext.value
      })
      
      return filteredHelp
    }
    return null
  }
  
  // Search help content
  const searchHelp = (query) => {
    const results = []
    const searchTerm = query.toLowerCase()
    
    Object.entries(helpContent).forEach(([key, content]) => {
      let relevance = 0
      
      // Title match (highest relevance)
      if (content.title.toLowerCase().includes(searchTerm)) {
        relevance += 10
      }
      
      // Description match
      if (content.description.toLowerCase().includes(searchTerm)) {
        relevance += 5
      }
      
      // Tips and examples match
      const allText = [
        ...(content.tips || []),
        ...(content.examples || []),
        ...(content.bestPractices || [])
      ].join(' ').toLowerCase()
      
      if (allText.includes(searchTerm)) {
        relevance += 2
      }
      
      if (relevance > 0) {
        results.push({
          key,
          content,
          relevance
        })
      }
    })
    
    return results.sort((a, b) => b.relevance - a.relevance)
  }
  
  // Get quick tips for current context
  const getQuickTips = () => {
    if (!currentContext.value) return []
    
    const tips = []
    
    // Context-specific tips
    if (currentContext.value.fieldType) {
      const fieldHelp = helpContent[`field-${currentContext.value.fieldType}`]
      if (fieldHelp && fieldHelp.tips) {
        tips.push(...fieldHelp.tips.slice(0, 2))
      }
    }
    
    // General tips based on user level
    if (userPreferences.helpLevel === 'beginner') {
      tips.push(
        'Use the preview button to test your form',
        'Save your work frequently with Ctrl+S'
      )
    }
    
    return tips
  }
  
  // Toggle help mode
  const toggleHelpMode = () => {
    isHelpMode.value = !isHelpMode.value
  }
  
  // Update user preferences
  const updatePreferences = (newPreferences) => {
    Object.assign(userPreferences, newPreferences)
    
    // Save to localStorage
    localStorage.setItem('formBuilderHelpPreferences', JSON.stringify(userPreferences))
  }
  
  // Load preferences from localStorage
  const loadPreferences = () => {
    try {
      const saved = localStorage.getItem('formBuilderHelpPreferences')
      if (saved) {
        const preferences = JSON.parse(saved)
        Object.assign(userPreferences, preferences)
      }
    } catch (error) {
      console.warn('Failed to load help preferences:', error)
    }
  }
  
  // Auto-show help based on context
  watch(currentContext, (newContext) => {
    if (!userPreferences.autoShowHelp || !newContext) return
    
    // Show help for complex features
    if (newContext.action === 'configuring-conditional-logic' && 
        userPreferences.helpLevel === 'beginner') {
      // Auto-show conditional logic help
      setTimeout(() => {
        getHelp('conditional-logic')
      }, 1000)
    }
  }, { deep: true })
  
  return {
    isHelpMode,
    currentContext,
    helpHistory,
    userPreferences,
    contextualSuggestions,
    behaviorBasedHelp,
    updateContext,
    getHelp,
    searchHelp,
    getQuickTips,
    toggleHelpMode,
    updatePreferences,
    loadPreferences
  }
}
