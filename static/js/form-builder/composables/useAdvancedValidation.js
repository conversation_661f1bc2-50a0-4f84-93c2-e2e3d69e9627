import { ref, reactive, computed } from 'vue'

export function useAdvancedValidation() {
  const validationRules = reactive(new Map())
  const validationErrors = reactive(new Map())
  const validationState = reactive(new Map())
  const customValidators = reactive(new Map())
  
  // Built-in validation rules
  const builtInRules = {
    required: {
      name: 'Required',
      description: 'Field must have a value',
      validate: (value, params) => {
        if (Array.isArray(value)) return value.length > 0
        return value !== null && value !== undefined && String(value).trim() !== ''
      },
      message: 'This field is required'
    },
    
    minLength: {
      name: 'Minimum Length',
      description: 'Field must have minimum number of characters',
      validate: (value, params) => {
        if (!value) return true // Let required handle empty values
        return String(value).length >= params.min
      },
      message: (params) => `Must be at least ${params.min} characters long`,
      params: [{ name: 'min', type: 'number', required: true }]
    },
    
    maxLength: {
      name: 'Maximum Length',
      description: 'Field must not exceed maximum number of characters',
      validate: (value, params) => {
        if (!value) return true
        return String(value).length <= params.max
      },
      message: (params) => `Must not exceed ${params.max} characters`,
      params: [{ name: 'max', type: 'number', required: true }]
    },
    
    email: {
      name: 'Email Format',
      description: 'Field must be a valid email address',
      validate: (value, params) => {
        if (!value) return true
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(value)
      },
      message: 'Please enter a valid email address'
    },
    
    url: {
      name: 'URL Format',
      description: 'Field must be a valid URL',
      validate: (value, params) => {
        if (!value) return true
        try {
          new URL(value)
          return true
        } catch {
          return false
        }
      },
      message: 'Please enter a valid URL'
    },
    
    phone: {
      name: 'Phone Number',
      description: 'Field must be a valid phone number',
      validate: (value, params) => {
        if (!value) return true
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
        return phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))
      },
      message: 'Please enter a valid phone number'
    },
    
    numeric: {
      name: 'Numeric',
      description: 'Field must contain only numbers',
      validate: (value, params) => {
        if (!value) return true
        return !isNaN(value) && !isNaN(parseFloat(value))
      },
      message: 'Please enter a valid number'
    },
    
    integer: {
      name: 'Integer',
      description: 'Field must be a whole number',
      validate: (value, params) => {
        if (!value) return true
        return Number.isInteger(Number(value))
      },
      message: 'Please enter a whole number'
    },
    
    min: {
      name: 'Minimum Value',
      description: 'Field must be greater than or equal to minimum value',
      validate: (value, params) => {
        if (!value) return true
        return Number(value) >= params.min
      },
      message: (params) => `Must be at least ${params.min}`,
      params: [{ name: 'min', type: 'number', required: true }]
    },
    
    max: {
      name: 'Maximum Value',
      description: 'Field must be less than or equal to maximum value',
      validate: (value, params) => {
        if (!value) return true
        return Number(value) <= params.max
      },
      message: (params) => `Must not exceed ${params.max}`,
      params: [{ name: 'max', type: 'number', required: true }]
    },
    
    pattern: {
      name: 'Pattern Match',
      description: 'Field must match a specific pattern',
      validate: (value, params) => {
        if (!value) return true
        const regex = new RegExp(params.pattern, params.flags || '')
        return regex.test(value)
      },
      message: (params) => params.message || 'Value does not match required pattern',
      params: [
        { name: 'pattern', type: 'text', required: true },
        { name: 'flags', type: 'text', required: false },
        { name: 'message', type: 'text', required: false }
      ]
    },
    
    date: {
      name: 'Valid Date',
      description: 'Field must be a valid date',
      validate: (value, params) => {
        if (!value) return true
        const date = new Date(value)
        return !isNaN(date.getTime())
      },
      message: 'Please enter a valid date'
    },
    
    dateRange: {
      name: 'Date Range',
      description: 'Date must be within specified range',
      validate: (value, params) => {
        if (!value) return true
        const date = new Date(value)
        if (isNaN(date.getTime())) return false
        
        if (params.min) {
          const minDate = new Date(params.min)
          if (date < minDate) return false
        }
        
        if (params.max) {
          const maxDate = new Date(params.max)
          if (date > maxDate) return false
        }
        
        return true
      },
      message: (params) => {
        if (params.min && params.max) {
          return `Date must be between ${params.min} and ${params.max}`
        } else if (params.min) {
          return `Date must be after ${params.min}`
        } else if (params.max) {
          return `Date must be before ${params.max}`
        }
        return 'Invalid date range'
      },
      params: [
        { name: 'min', type: 'date', required: false },
        { name: 'max', type: 'date', required: false }
      ]
    },
    
    fileSize: {
      name: 'File Size',
      description: 'File must be within size limits',
      validate: (value, params) => {
        if (!value || !value.length) return true
        
        for (const file of value) {
          if (params.maxSize && file.size > params.maxSize) return false
          if (params.minSize && file.size < params.minSize) return false
        }
        
        return true
      },
      message: (params) => {
        if (params.maxSize) {
          return `File size must not exceed ${formatFileSize(params.maxSize)}`
        }
        return 'Invalid file size'
      },
      params: [
        { name: 'maxSize', type: 'number', required: false },
        { name: 'minSize', type: 'number', required: false }
      ]
    },
    
    fileType: {
      name: 'File Type',
      description: 'File must be of allowed types',
      validate: (value, params) => {
        if (!value || !value.length) return true
        
        const allowedTypes = params.types || []
        for (const file of value) {
          const fileType = file.type || ''
          const fileExt = file.name.split('.').pop()?.toLowerCase()
          
          const isAllowed = allowedTypes.some(type => {
            if (type.startsWith('.')) {
              return fileExt === type.substring(1)
            }
            return fileType.startsWith(type)
          })
          
          if (!isAllowed) return false
        }
        
        return true
      },
      message: (params) => `Only ${params.types?.join(', ')} files are allowed`,
      params: [{ name: 'types', type: 'array', required: true }]
    },
    
    conditional: {
      name: 'Conditional Validation',
      description: 'Validation depends on other field values',
      validate: (value, params, formData) => {
        const condition = params.condition
        if (!condition) return true
        
        // Evaluate condition
        const conditionMet = evaluateCondition(condition, formData)
        
        if (!conditionMet) return true // Skip validation if condition not met
        
        // Apply nested validation rules
        if (params.rules) {
          for (const rule of params.rules) {
            const ruleValidator = builtInRules[rule.type] || customValidators.get(rule.type)
            if (ruleValidator && !ruleValidator.validate(value, rule.params, formData)) {
              return false
            }
          }
        }
        
        return true
      },
      message: (params) => params.message || 'Validation failed',
      params: [
        { name: 'condition', type: 'object', required: true },
        { name: 'rules', type: 'array', required: false },
        { name: 'message', type: 'text', required: false }
      ]
    },
    
    unique: {
      name: 'Unique Value',
      description: 'Value must be unique (requires external validation)',
      validate: async (value, params) => {
        if (!value) return true
        
        // This would typically make an API call to check uniqueness
        if (params.apiEndpoint) {
          try {
            const response = await fetch(params.apiEndpoint, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ value, field: params.field })
            })
            const result = await response.json()
            return result.isUnique
          } catch (error) {
            console.error('Unique validation error:', error)
            return true // Fail gracefully
          }
        }
        
        return true
      },
      message: 'This value is already taken',
      params: [
        { name: 'apiEndpoint', type: 'text', required: true },
        { name: 'field', type: 'text', required: true }
      ],
      async: true
    }
  }
  
  // Add validation rule to a field
  const addValidationRule = (fieldId, ruleType, params = {}) => {
    if (!validationRules.has(fieldId)) {
      validationRules.set(fieldId, [])
    }
    
    const rules = validationRules.get(fieldId)
    rules.push({
      id: `${fieldId}-${ruleType}-${Date.now()}`,
      type: ruleType,
      params,
      enabled: true
    })
  }
  
  // Remove validation rule
  const removeValidationRule = (fieldId, ruleId) => {
    if (!validationRules.has(fieldId)) return
    
    const rules = validationRules.get(fieldId)
    const index = rules.findIndex(rule => rule.id === ruleId)
    if (index > -1) {
      rules.splice(index, 1)
    }
  }
  
  // Register custom validator
  const registerCustomValidator = (name, validator) => {
    customValidators.set(name, validator)
  }
  
  // Validate a single field
  const validateField = async (fieldId, value, formData = {}) => {
    const rules = validationRules.get(fieldId) || []
    const errors = []
    
    for (const rule of rules) {
      if (!rule.enabled) continue
      
      const validator = builtInRules[rule.type] || customValidators.get(rule.type)
      if (!validator) continue
      
      try {
        let isValid
        if (validator.async) {
          isValid = await validator.validate(value, rule.params, formData)
        } else {
          isValid = validator.validate(value, rule.params, formData)
        }
        
        if (!isValid) {
          const message = typeof validator.message === 'function' 
            ? validator.message(rule.params)
            : validator.message
          
          errors.push({
            ruleId: rule.id,
            ruleType: rule.type,
            message,
            params: rule.params
          })
        }
      } catch (error) {
        console.error(`Validation error for rule ${rule.type}:`, error)
        errors.push({
          ruleId: rule.id,
          ruleType: rule.type,
          message: 'Validation error occurred',
          params: rule.params
        })
      }
    }
    
    validationErrors.set(fieldId, errors)
    validationState.set(fieldId, {
      isValid: errors.length === 0,
      errors,
      lastValidated: Date.now()
    })
    
    return errors.length === 0
  }
  
  // Validate entire form
  const validateForm = async (formData) => {
    const results = {}
    const promises = []
    
    for (const [fieldId] of validationRules) {
      const value = formData[fieldId]
      promises.push(
        validateField(fieldId, value, formData).then(isValid => {
          results[fieldId] = isValid
        })
      )
    }
    
    await Promise.all(promises)
    
    const isFormValid = Object.values(results).every(isValid => isValid)
    return { isValid: isFormValid, fieldResults: results }
  }
  
  // Get validation errors for a field
  const getFieldErrors = (fieldId) => {
    return validationErrors.get(fieldId) || []
  }
  
  // Get validation state for a field
  const getFieldState = (fieldId) => {
    return validationState.get(fieldId) || { isValid: true, errors: [], lastValidated: null }
  }
  
  // Clear validation errors
  const clearFieldErrors = (fieldId) => {
    validationErrors.set(fieldId, [])
    validationState.set(fieldId, { isValid: true, errors: [], lastValidated: null })
  }
  
  // Get available validation rules
  const getAvailableRules = () => {
    const rules = { ...builtInRules }
    for (const [name, validator] of customValidators) {
      rules[name] = validator
    }
    return rules
  }
  
  // Utility functions
  const evaluateCondition = (condition, formData) => {
    const { field, operator, value } = condition
    const fieldValue = formData[field]
    
    switch (operator) {
      case 'equals':
        return fieldValue === value
      case 'not_equals':
        return fieldValue !== value
      case 'contains':
        return String(fieldValue || '').includes(value)
      case 'not_contains':
        return !String(fieldValue || '').includes(value)
      case 'is_empty':
        return !fieldValue || fieldValue === ''
      case 'is_not_empty':
        return fieldValue && fieldValue !== ''
      case 'greater_than':
        return Number(fieldValue) > Number(value)
      case 'less_than':
        return Number(fieldValue) < Number(value)
      default:
        return true
    }
  }
  
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  // Computed properties
  const hasErrors = computed(() => {
    for (const [, errors] of validationErrors) {
      if (errors.length > 0) return true
    }
    return false
  })
  
  const errorCount = computed(() => {
    let count = 0
    for (const [, errors] of validationErrors) {
      count += errors.length
    }
    return count
  })
  
  return {
    validationRules,
    validationErrors,
    validationState,
    builtInRules,
    hasErrors,
    errorCount,
    addValidationRule,
    removeValidationRule,
    registerCustomValidator,
    validateField,
    validateForm,
    getFieldErrors,
    getFieldState,
    clearFieldErrors,
    getAvailableRules
  }
}
