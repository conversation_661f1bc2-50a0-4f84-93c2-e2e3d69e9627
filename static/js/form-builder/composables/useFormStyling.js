import { ref, computed, watch } from 'vue'

export function useFormStyling(initialCustomization = {}) {
  const customization = ref({
    theme: 'default',
    colors: {
      primary: '#3B82F6',
      secondary: '#6B7280',
      background: '#FFFFFF',
      text: '#1F2937',
      border: '#D1D5DB',
      error: '#EF4444',
      success: '#10B981'
    },
    typography: {
      fontFamily: 'system',
      fontSize: 16,
      lineHeight: 1.5,
      fontWeight: 'normal'
    },
    spacing: {
      fieldSpacing: 16,
      formPadding: 24,
      sectionSpacing: 32,
      borderRadius: 6
    },
    layout: {
      maxWidth: '600px',
      columns: 1,
      fieldWidth: 'full'
    },
    responsive: {
      desktop: { columns: 1, fieldWidth: 'full', fontSize: 16 },
      tablet: { columns: 1, fieldWidth: 'full', fontSize: 16 },
      mobile: { columns: 1, fieldWidth: 'full', fontSize: 16 }
    },
    animations: {
      enabled: true,
      duration: 200,
      easing: 'ease-in-out'
    },
    customCSS: '',
    ...initialCustomization
  })

  // Predefined themes
  const themes = {
    default: {
      colors: {
        primary: '#3B82F6',
        secondary: '#6B7280',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#D1D5DB',
        error: '#EF4444',
        success: '#10B981'
      },
      typography: {
        fontFamily: 'system',
        fontSize: 16,
        lineHeight: 1.5
      }
    },
    minimal: {
      colors: {
        primary: '#6B7280',
        secondary: '#9CA3AF',
        background: '#F9FAFB',
        text: '#374151',
        border: '#E5E7EB',
        error: '#DC2626',
        success: '#059669'
      },
      typography: {
        fontFamily: 'Inter',
        fontSize: 15,
        lineHeight: 1.6
      }
    },
    modern: {
      colors: {
        primary: '#8B5CF6',
        secondary: '#A78BFA',
        background: '#FFFFFF',
        text: '#111827',
        border: '#E5E7EB',
        error: '#F59E0B',
        success: '#10B981'
      },
      typography: {
        fontFamily: 'Poppins',
        fontSize: 16,
        lineHeight: 1.5
      }
    },
    dark: {
      colors: {
        primary: '#60A5FA',
        secondary: '#9CA3AF',
        background: '#1F2937',
        text: '#F9FAFB',
        border: '#374151',
        error: '#F87171',
        success: '#34D399'
      },
      typography: {
        fontFamily: 'system',
        fontSize: 16,
        lineHeight: 1.5
      }
    }
  }

  // Font families
  const fontFamilies = {
    system: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    Inter: '"Inter", system-ui, sans-serif',
    Roboto: '"Roboto", system-ui, sans-serif',
    'Open Sans': '"Open Sans", system-ui, sans-serif',
    Lato: '"Lato", system-ui, sans-serif',
    Poppins: '"Poppins", system-ui, sans-serif',
    Montserrat: '"Montserrat", system-ui, sans-serif'
  }

  // Computed CSS variables
  const cssVariables = computed(() => {
    const vars = {
      '--form-primary-color': customization.value.colors.primary,
      '--form-secondary-color': customization.value.colors.secondary,
      '--form-background-color': customization.value.colors.background,
      '--form-text-color': customization.value.colors.text,
      '--form-border-color': customization.value.colors.border,
      '--form-error-color': customization.value.colors.error,
      '--form-success-color': customization.value.colors.success,
      '--form-font-family': fontFamilies[customization.value.typography.fontFamily] || fontFamilies.system,
      '--form-font-size': `${customization.value.typography.fontSize}px`,
      '--form-line-height': customization.value.typography.lineHeight,
      '--form-font-weight': customization.value.typography.fontWeight,
      '--form-field-spacing': `${customization.value.spacing.fieldSpacing}px`,
      '--form-padding': `${customization.value.spacing.formPadding}px`,
      '--form-section-spacing': `${customization.value.spacing.sectionSpacing}px`,
      '--form-border-radius': `${customization.value.spacing.borderRadius}px`,
      '--form-max-width': customization.value.layout.maxWidth,
      '--form-animation-duration': customization.value.animations.enabled ? `${customization.value.animations.duration}ms` : '0ms',
      '--form-animation-easing': customization.value.animations.easing
    }

    return vars
  })

  // Generate complete CSS
  const generatedCSS = computed(() => {
    const baseCSS = `
      .form-container {
        max-width: var(--form-max-width);
        margin: 0 auto;
        padding: var(--form-padding);
        background-color: var(--form-background-color);
        color: var(--form-text-color);
        font-family: var(--form-font-family);
        font-size: var(--form-font-size);
        line-height: var(--form-line-height);
        font-weight: var(--form-font-weight);
      }

      .form-field {
        margin-bottom: var(--form-field-spacing);
        transition: all var(--form-animation-duration) var(--form-animation-easing);
      }

      .form-section {
        margin-bottom: var(--form-section-spacing);
      }

      .field-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--form-text-color);
      }

      .field-input,
      .field-textarea,
      .field-select {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--form-border-color);
        border-radius: var(--form-border-radius);
        background-color: var(--form-background-color);
        color: var(--form-text-color);
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        transition: all var(--form-animation-duration) var(--form-animation-easing);
      }

      .field-input:focus,
      .field-textarea:focus,
      .field-select:focus {
        outline: none;
        border-color: var(--form-primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .field-error {
        color: var(--form-error-color);
        font-size: 0.875em;
        margin-top: 4px;
      }

      .field-help {
        color: var(--form-secondary-color);
        font-size: 0.875em;
        margin-top: 4px;
      }

      .required-indicator {
        color: var(--form-error-color);
        margin-left: 4px;
      }

      .submit-button {
        background-color: var(--form-primary-color);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: var(--form-border-radius);
        font-family: inherit;
        font-size: inherit;
        font-weight: 500;
        cursor: pointer;
        transition: all var(--form-animation-duration) var(--form-animation-easing);
      }

      .submit-button:hover {
        opacity: 0.9;
        transform: translateY(-1px);
      }

      .submit-button:active {
        transform: translateY(0);
      }

      /* Radio and Checkbox Styles */
      .radio-group,
      .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .radio-label,
      .checkbox-label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
      }

      .radio-input,
      .checkbox-input {
        accent-color: var(--form-primary-color);
      }

      /* Multi-column layouts */
      .form-columns-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--form-field-spacing);
      }

      .form-columns-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: var(--form-field-spacing);
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .form-columns-2,
        .form-columns-3 {
          grid-template-columns: 1fr;
        }
        
        .form-container {
          padding: calc(var(--form-padding) * 0.75);
        }
        
        .field-input,
        .field-textarea,
        .field-select {
          font-size: 16px; /* Prevent zoom on iOS */
        }
      }
    `

    return baseCSS + '\n\n' + customization.value.customCSS
  })

  // Apply theme
  const applyTheme = (themeId) => {
    if (themes[themeId]) {
      customization.value.theme = themeId
      Object.assign(customization.value.colors, themes[themeId].colors)
      Object.assign(customization.value.typography, themes[themeId].typography)
    }
  }

  // Reset to defaults
  const resetToDefaults = () => {
    applyTheme('default')
    customization.value.spacing = {
      fieldSpacing: 16,
      formPadding: 24,
      sectionSpacing: 32,
      borderRadius: 6
    }
    customization.value.layout = {
      maxWidth: '600px',
      columns: 1,
      fieldWidth: 'full'
    }
    customization.value.animations = {
      enabled: true,
      duration: 200,
      easing: 'ease-in-out'
    }
    customization.value.customCSS = ''
  }

  // Export configuration
  const exportConfiguration = () => {
    return JSON.stringify(customization.value, null, 2)
  }

  // Import configuration
  const importConfiguration = (config) => {
    try {
      const parsed = typeof config === 'string' ? JSON.parse(config) : config
      customization.value = { ...customization.value, ...parsed }
      return true
    } catch (error) {
      console.error('Failed to import configuration:', error)
      return false
    }
  }

  // Apply styles to DOM
  const applyStylesToDOM = (targetElement = document.documentElement) => {
    Object.entries(cssVariables.value).forEach(([property, value]) => {
      targetElement.style.setProperty(property, value)
    })

    // Apply custom CSS
    let styleElement = document.getElementById('form-custom-styles')
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = 'form-custom-styles'
      document.head.appendChild(styleElement)
    }
    styleElement.textContent = generatedCSS.value
  }

  // Watch for changes and apply to DOM
  watch(customization, () => {
    applyStylesToDOM()
  }, { deep: true })

  return {
    customization,
    themes,
    fontFamilies,
    cssVariables,
    generatedCSS,
    applyTheme,
    resetToDefaults,
    exportConfiguration,
    importConfiguration,
    applyStylesToDOM
  }
}
