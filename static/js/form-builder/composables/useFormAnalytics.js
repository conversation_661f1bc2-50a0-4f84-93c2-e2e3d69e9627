import { ref, reactive, computed, watch } from 'vue'

export function useFormAnalytics(formId, options = {}) {
  const {
    enableRealTime = true,
    trackFieldInteractions = true,
    trackPageViews = true,
    trackSubmissions = true,
    trackErrors = true,
    trackPerformance = true,
    batchSize = 10,
    flushInterval = 5000, // 5 seconds
    apiEndpoint = '/api/analytics'
  } = options

  // Analytics state
  const analytics = reactive({
    sessionId: generateSessionId(),
    startTime: Date.now(),
    events: [],
    metrics: {
      pageViews: {},
      fieldInteractions: {},
      timeSpent: {},
      errors: [],
      submissions: 0,
      abandonment: {
        rate: 0,
        points: []
      },
      completion: {
        rate: 0,
        averageTime: 0
      },
      performance: {
        loadTime: 0,
        renderTime: 0,
        interactionDelay: 0
      }
    },
    realTimeData: {
      currentUsers: 0,
      activeFields: new Set(),
      currentPage: null,
      lastActivity: Date.now()
    }
  })

  const isTracking = ref(false)
  const eventQueue = ref([])
  let flushTimer = null

  // Computed analytics
  const conversionRate = computed(() => {
    const totalViews = Object.values(analytics.metrics.pageViews).reduce((sum, views) => sum + views, 0)
    return totalViews > 0 ? (analytics.metrics.submissions / totalViews) * 100 : 0
  })

  const averageTimePerPage = computed(() => {
    const times = Object.values(analytics.metrics.timeSpent)
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 0
  })

  const mostInteractedFields = computed(() => {
    return Object.entries(analytics.metrics.fieldInteractions)
      .sort(([,a], [,b]) => b.interactions - a.interactions)
      .slice(0, 5)
      .map(([fieldId, data]) => ({ fieldId, ...data }))
  })

  const dropOffPoints = computed(() => {
    return analytics.metrics.abandonment.points
      .sort((a, b) => b.rate - a.rate)
      .slice(0, 3)
  })

  // Event tracking functions
  const trackEvent = (eventType, data = {}) => {
    if (!isTracking.value) return

    const event = {
      id: generateEventId(),
      type: eventType,
      timestamp: Date.now(),
      sessionId: analytics.sessionId,
      formId,
      data: {
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        url: window.location.href,
        ...data
      }
    }

    analytics.events.push(event)
    eventQueue.value.push(event)

    // Process event for real-time metrics
    processEventForMetrics(event)

    // Auto-flush if queue is full
    if (eventQueue.value.length >= batchSize) {
      flushEvents()
    }
  }

  const trackPageView = (pageId, pageTitle = '') => {
    if (!trackPageViews) return

    const previousPage = analytics.realTimeData.currentPage
    const now = Date.now()

    // Track time spent on previous page
    if (previousPage && analytics.metrics.timeSpent[previousPage.id]) {
      const timeSpent = now - previousPage.startTime
      analytics.metrics.timeSpent[previousPage.id] += timeSpent
    }

    // Update current page
    analytics.realTimeData.currentPage = {
      id: pageId,
      title: pageTitle,
      startTime: now
    }

    // Track page view
    if (!analytics.metrics.pageViews[pageId]) {
      analytics.metrics.pageViews[pageId] = 0
    }
    analytics.metrics.pageViews[pageId]++

    trackEvent('page_view', {
      pageId,
      pageTitle,
      previousPageId: previousPage?.id,
      timeOnPreviousPage: previousPage ? now - previousPage.startTime : 0
    })
  }

  const trackFieldInteraction = (fieldId, interactionType, value = null) => {
    if (!trackFieldInteractions) return

    analytics.realTimeData.activeFields.add(fieldId)
    analytics.realTimeData.lastActivity = Date.now()

    // Initialize field metrics
    if (!analytics.metrics.fieldInteractions[fieldId]) {
      analytics.metrics.fieldInteractions[fieldId] = {
        interactions: 0,
        focus: 0,
        blur: 0,
        change: 0,
        errors: 0,
        timeSpent: 0,
        firstInteraction: Date.now(),
        lastInteraction: Date.now()
      }
    }

    const fieldMetrics = analytics.metrics.fieldInteractions[fieldId]
    fieldMetrics.interactions++
    fieldMetrics[interactionType]++
    fieldMetrics.lastInteraction = Date.now()

    trackEvent('field_interaction', {
      fieldId,
      interactionType,
      value: typeof value === 'string' ? value.substring(0, 100) : value, // Limit value length
      timestamp: Date.now()
    })
  }

  const trackError = (errorType, errorMessage, context = {}) => {
    if (!trackErrors) return

    const error = {
      id: generateEventId(),
      type: errorType,
      message: errorMessage,
      timestamp: Date.now(),
      context
    }

    analytics.metrics.errors.push(error)

    trackEvent('error', {
      errorType,
      errorMessage,
      context,
      pageId: analytics.realTimeData.currentPage?.id
    })
  }

  const trackSubmission = (submissionData = {}) => {
    if (!trackSubmissions) return

    analytics.metrics.submissions++
    
    const totalTime = Date.now() - analytics.startTime
    analytics.metrics.completion.averageTime = 
      (analytics.metrics.completion.averageTime + totalTime) / analytics.metrics.submissions

    trackEvent('form_submission', {
      submissionId: submissionData.id,
      totalTime,
      pageCount: Object.keys(analytics.metrics.pageViews).length,
      fieldInteractions: Object.keys(analytics.metrics.fieldInteractions).length,
      errors: analytics.metrics.errors.length
    })
  }

  const trackPerformanceMetric = (metricName, value, context = {}) => {
    if (!trackPerformance) return

    analytics.metrics.performance[metricName] = value

    trackEvent('performance', {
      metricName,
      value,
      context
    })
  }

  const trackAbandonmentPoint = (pageId, reason = 'unknown') => {
    const abandonmentPoint = {
      pageId,
      timestamp: Date.now(),
      reason,
      timeSpent: Date.now() - analytics.startTime,
      completionPercentage: calculateCompletionPercentage()
    }

    analytics.metrics.abandonment.points.push(abandonmentPoint)

    trackEvent('abandonment', abandonmentPoint)
  }

  // Utility functions
  const processEventForMetrics = (event) => {
    switch (event.type) {
      case 'field_interaction':
        if (event.data.interactionType === 'focus') {
          analytics.realTimeData.activeFields.add(event.data.fieldId)
        }
        break
      case 'error':
        if (event.data.fieldId) {
          const fieldMetrics = analytics.metrics.fieldInteractions[event.data.fieldId]
          if (fieldMetrics) {
            fieldMetrics.errors++
          }
        }
        break
    }
  }

  const calculateCompletionPercentage = () => {
    const totalPages = Object.keys(analytics.metrics.pageViews).length
    const currentPageIndex = analytics.realTimeData.currentPage ? 
      Object.keys(analytics.metrics.pageViews).indexOf(analytics.realTimeData.currentPage.id) : 0
    return totalPages > 0 ? (currentPageIndex / totalPages) * 100 : 0
  }

  const generateSessionId = () => {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  const generateEventId = () => {
    return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  const flushEvents = async () => {
    if (eventQueue.value.length === 0) return

    const eventsToSend = [...eventQueue.value]
    eventQueue.value = []

    try {
      await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formId,
          sessionId: analytics.sessionId,
          events: eventsToSend,
          metrics: analytics.metrics
        })
      })
    } catch (error) {
      console.error('Failed to send analytics events:', error)
      // Re-queue events on failure
      eventQueue.value.unshift(...eventsToSend)
    }
  }

  const startTracking = () => {
    if (isTracking.value) return

    isTracking.value = true
    analytics.startTime = Date.now()

    // Set up auto-flush timer
    if (enableRealTime) {
      flushTimer = setInterval(flushEvents, flushInterval)
    }

    // Track initial page load
    trackPerformanceMetric('loadTime', performance.now())

    // Set up performance observers
    if (trackPerformance && 'PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'measure') {
            trackPerformanceMetric(entry.name, entry.duration)
          }
        }
      })
      observer.observe({ entryTypes: ['measure'] })
    }

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        trackEvent('page_hidden', {
          timeVisible: Date.now() - analytics.realTimeData.lastActivity
        })
      } else {
        trackEvent('page_visible')
        analytics.realTimeData.lastActivity = Date.now()
      }
    })

    // Track beforeunload for abandonment
    window.addEventListener('beforeunload', () => {
      if (analytics.metrics.submissions === 0) {
        trackAbandonmentPoint(
          analytics.realTimeData.currentPage?.id || 'unknown',
          'page_unload'
        )
        // Synchronous send for unload
        navigator.sendBeacon(apiEndpoint, JSON.stringify({
          formId,
          sessionId: analytics.sessionId,
          events: eventQueue.value,
          metrics: analytics.metrics
        }))
      }
    })
  }

  const stopTracking = () => {
    isTracking.value = false
    
    if (flushTimer) {
      clearInterval(flushTimer)
      flushTimer = null
    }

    // Final flush
    flushEvents()
  }

  const getAnalyticsReport = () => {
    return {
      sessionId: analytics.sessionId,
      duration: Date.now() - analytics.startTime,
      metrics: analytics.metrics,
      conversionRate: conversionRate.value,
      averageTimePerPage: averageTimePerPage.value,
      mostInteractedFields: mostInteractedFields.value,
      dropOffPoints: dropOffPoints.value,
      realTimeData: {
        ...analytics.realTimeData,
        activeFields: Array.from(analytics.realTimeData.activeFields)
      }
    }
  }

  const exportAnalytics = (format = 'json') => {
    const report = getAnalyticsReport()
    
    if (format === 'csv') {
      return convertToCSV(report)
    }
    
    return JSON.stringify(report, null, 2)
  }

  const convertToCSV = (data) => {
    // Convert analytics data to CSV format
    const events = analytics.events.map(event => ({
      timestamp: new Date(event.timestamp).toISOString(),
      type: event.type,
      pageId: event.data.pageId || '',
      fieldId: event.data.fieldId || '',
      value: event.data.value || ''
    }))

    const headers = Object.keys(events[0] || {})
    const csvContent = [
      headers.join(','),
      ...events.map(event => headers.map(header => event[header] || '').join(','))
    ].join('\n')

    return csvContent
  }

  // Watch for real-time updates
  if (enableRealTime) {
    watch(() => analytics.realTimeData.lastActivity, () => {
      // Update activity timestamp
    })
  }

  return {
    analytics: readonly(analytics),
    isTracking,
    conversionRate,
    averageTimePerPage,
    mostInteractedFields,
    dropOffPoints,
    trackEvent,
    trackPageView,
    trackFieldInteraction,
    trackError,
    trackSubmission,
    trackPerformanceMetric,
    trackAbandonmentPoint,
    startTracking,
    stopTracking,
    flushEvents,
    getAnalyticsReport,
    exportAnalytics
  }
}
