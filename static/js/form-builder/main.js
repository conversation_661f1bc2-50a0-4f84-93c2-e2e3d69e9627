// Form Builder main entry point
import '../main.js' // Import base styles and setup
import { createApp } from 'vue'
import FormBuilderMain from './components/FormBuilderMain.vue'
import { createFormBuilderStore } from './stores/formBuilderStore'

// Global styles
import './styles/main.css'

// Create and mount the Vue app
window.initFormBuilder = function(elementId, formData, csrfToken) {
  console.log('initFormBuilder called with:', { elementId, formData, csrfToken })

  try {
    const app = createApp({
      components: {
        FormBuilderMain
      },
      data() {
        return {
          formData: formData || {},
          csrfToken: csrfToken || ''
        }
      },
      mounted() {
        console.log('Vue app mounted successfully')
        // Hide loading state
        const loadingState = document.getElementById('loading-state')
        if (loadingState) {
          loadingState.style.display = 'none'
        }
      },
      template: `
        <form-builder-main
          :form-data="formData"
          :csrf-token="csrfToken"
        ></form-builder-main>
      `
    })

    // Create and provide the store
    const store = createFormBuilderStore()
    app.provide('formBuilderStore', store)

    // Global error handler
    app.config.errorHandler = (err, instance, info) => {
      console.error('Form Builder Error:', err, info)
    }

    console.log('Mounting Vue app to:', elementId)
    // Mount the app
    const mountedApp = app.mount(elementId)
    console.log('Vue app mounted:', mountedApp)

    return app
  } catch (error) {
    console.error('Error in initFormBuilder:', error)
    throw error
  }
}

// Initialize the form builder application
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM Content Loaded - Initializing Form Builder')

  const formBuilderElement = document.getElementById('form-builder-app')
  console.log('Form Builder Element:', formBuilderElement)
  console.log('Form Builder Data:', window.formBuilderData)

  if (!formBuilderElement) {
    console.error('Form builder element not found!')
    return
  }

  if (!window.formBuilderData) {
    console.error('Form builder data not found!')
    return
  }

  try {
    console.log('Initializing Vue app...')
    const app = window.initFormBuilder(
      '#form-builder-app',
      window.formBuilderData.formData,
      window.formBuilderData.csrfToken
    )
    console.log('Vue app initialized successfully:', app)
  } catch (error) {
    console.error('Error initializing form builder:', error)

    // Create a simple fallback to show that Vue is working
    try {
      console.log('Creating fallback Vue app...')
      const fallbackApp = createApp({
        data() {
          return {
            message: 'Vue.js is working! Form builder failed to initialize.',
            formData: window.formBuilderData?.formData || {}
          }
        },
        template: `
          <div class="p-8 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h2 class="text-xl font-bold text-yellow-800 mb-4">{{ message }}</h2>
            <div class="text-sm text-yellow-700">
              <p><strong>Form ID:</strong> {{ formData.id || 'N/A' }}</p>
              <p><strong>Form Name:</strong> {{ formData.name || 'N/A' }}</p>
              <p><strong>Fields Count:</strong> {{ (formData.fields || []).length }}</p>
            </div>
            <div class="mt-4">
              <p class="text-sm text-yellow-600">Check the browser console for detailed error information.</p>
            </div>
          </div>
        `
      })
      fallbackApp.mount('#form-builder-app')
      console.log('Fallback Vue app mounted successfully')
    } catch (fallbackError) {
      console.error('Even fallback Vue app failed:', fallbackError)
    }
  }
})
